import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';

class ReservationDetailsPage extends StatefulWidget {
  final ReservationModel reservation;

  const ReservationDetailsPage({
    super.key,
    required this.reservation,
  });

  @override
  State<ReservationDetailsPage> createState() => _ReservationDetailsPageState();
}

class _ReservationDetailsPageState extends State<ReservationDetailsPage> {
  final ReservationsApiService _reservationsApiService = getIt<ReservationsApiService>();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final reservation = widget.reservation;
    final isConfirmed = reservation.confirmed;
    final statusColor = isConfirmed ? Colors.green : Colors.orange;
    final statusText = isConfirmed ? 'مؤكدة' : 'في الانتظار';

    return EnhancedPageLayout(
      title: 'تفاصيل الحجز #${reservation.id}',
      hasBottomNavigation: false,
      floatingActionButton: _buildFloatingActionMenu(reservation),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            _buildStatusCard(statusText, statusColor),
            
            const SizedBox(height: 16),
            
            // Property Details Card
            if (reservation.item != null) _buildPropertyCard(reservation.item!),
            
            const SizedBox(height: 16),
            
            // Guest Details Card
            _buildGuestCard(reservation),
            
            const SizedBox(height: 16),
            
            // Reservation Details Card
            _buildReservationCard(reservation),
            
            const SizedBox(height: 16),
            
            // Pricing Details Card
            _buildPricingCard(reservation),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            _buildActionButtons(reservation, isConfirmed),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(String statusText, Color statusColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                widget.reservation.confirmed ? Icons.check_circle : Icons.schedule,
                color: statusColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حالة الحجز',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    statusText,
                    style: AppTextStyles.font18Bold.copyWith(
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyCard(PropertyItemModel property) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل العقار',
              style: AppTextStyles.font16Bold,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[200],
                    child: property.image != null
                        ? Image.network(
                            property.image!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(Icons.home, color: Colors.grey[400], size: 32);
                            },
                          )
                        : Icon(Icons.home, color: Colors.grey[400], size: 32),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        property.title,
                        style: AppTextStyles.font16SemiBold,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'السعر: ${property.price.toStringAsFixed(0)} ر.س/ليلة',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: AppColors.yellow,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (property.noGuests != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'يستوعب: ${property.noGuests} ضيوف',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            if (property.content.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'الوصف:',
                style: AppTextStyles.font14SemiBold,
              ),
              const SizedBox(height: 4),
              Text(
                property.content,
                style: AppTextStyles.font14Regular,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGuestCard(ReservationModel reservation) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الضيف',
              style: AppTextStyles.font16Bold,
            ),
            const SizedBox(height: 12),
            _buildDetailRow('الاسم:', reservation.guestName ?? 'غير محدد'),
            if (reservation.guestPhone != null) ...[
              _buildDetailRow('رقم الهاتف:', reservation.guestPhone!),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _callGuest(reservation.guestPhone!),
                      icon: const Icon(Icons.phone, size: 18),
                      label: const Text('اتصال'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.green,
                        side: const BorderSide(color: Colors.green),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _copyPhoneNumber(reservation.guestPhone!),
                      icon: const Icon(Icons.copy, size: 18),
                      label: const Text('نسخ'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.yellow,
                        side: const BorderSide(color: AppColors.yellow),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReservationCard(ReservationModel reservation) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الحجز',
              style: AppTextStyles.font16Bold,
            ),
            const SizedBox(height: 12),
            _buildDetailRow('رقم الحجز:', '#${reservation.id}'),
            _buildDetailRow(
              'تاريخ الحجز:',
              DateFormat('yyyy/MM/dd HH:mm').format(
                DateTime.parse(reservation.createdAt),
              ),
            ),
            _buildDetailRow(
              'تاريخ الوصول:',
              DateFormat('yyyy/MM/dd HH:mm').format(
                DateTime.parse(reservation.reservationFrom),
              ),
            ),
            _buildDetailRow(
              'تاريخ المغادرة:',
              DateFormat('yyyy/MM/dd HH:mm').format(
                DateTime.parse(reservation.reservationTo),
              ),
            ),
            _buildDetailRow('عدد الليالي:', '${reservation.durationInDays} ليلة'),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingCard(ReservationModel reservation) {
    final subtotal = reservation.totalAmount;
    final commission = subtotal * 0.1; // Assuming 10% commission
    final total = subtotal + commission;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الأسعار',
              style: AppTextStyles.font16Bold,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              'المبلغ الفرعي:',
              '${subtotal.toStringAsFixed(0)} ر.س',
            ),
            _buildDetailRow(
              'العمولة (10%):',
              '${commission.toStringAsFixed(0)} ر.س',
            ),
            const Divider(),
            _buildDetailRow(
              'المبلغ الإجمالي:',
              '${total.toStringAsFixed(0)} ر.س',
              valueStyle: AppTextStyles.font16Bold.copyWith(
                color: AppColors.yellow,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {TextStyle? valueStyle}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: valueStyle ?? AppTextStyles.font14SemiBold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ReservationModel reservation, bool isConfirmed) {
    if (isConfirmed) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _viewProperty(reservation.item),
              icon: const Icon(Icons.home),
              label: const Text('عرض العقار'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.yellow,
                foregroundColor: AppColors.black,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : () => _confirmReservation(reservation.id),
                icon: _isLoading 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.check_circle),
                label: Text(_isLoading ? 'جاري القبول...' : 'قبول الحجز'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : () => _showDeclineDialog(reservation),
                icon: const Icon(Icons.cancel),
                label: const Text('رفض الحجز'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _callGuest(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن إجراء المكالمة')),
        );
      }
    }
  }

  void _copyPhoneNumber(String phoneNumber) {
    Clipboard.setData(ClipboardData(text: phoneNumber));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ رقم الهاتف')),
    );
  }

  void _viewProperty(PropertyItemModel? property) {
    if (property != null) {
      // TODO: Navigate to property details page
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('سيتم إضافة صفحة تفاصيل العقار قريباً')),
      );
    }
  }

  Future<void> _confirmReservation(int reservationId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _reservationsApiService.confirmReservation(reservationId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم قبول الحجز بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في قبول الحجز: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showDeclineDialog(ReservationModel reservation) {
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفض الحجز'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من رفض حجز #${reservation.id}؟'),
            const SizedBox(height: 16),
            const Text(
              'سبب الرفض (اختياري):',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                hintText: 'اكتب سبب الرفض...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _rejectReservation(reservation.id, reason: reasonController.text.trim());
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('رفض', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _rejectReservation(int reservationId, {String? reason}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _reservationsApiService.rejectReservation(reservationId, reason: reason);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم رفض الحجز'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفض الحجز: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildFloatingActionMenu(ReservationModel reservation) {
    return FloatingActionButton(
      onPressed: () => _showActionMenu(reservation),
      backgroundColor: AppColors.yellow,
      foregroundColor: AppColors.black,
      child: const Icon(Icons.more_vert),
    );
  }

  void _showActionMenu(ReservationModel reservation) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'إجراءات إضافية',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 20),
            _buildActionTile(
              icon: Icons.share,
              title: 'مشاركة تفاصيل الحجز',
              subtitle: 'مشاركة معلومات الحجز',
              onTap: () {
                Navigator.pop(context);
                _shareReservationDetails(reservation);
              },
            ),
            _buildActionTile(
              icon: Icons.copy,
              title: 'نسخ رقم الحجز',
              subtitle: 'نسخ رقم الحجز للحافظة',
              onTap: () {
                Navigator.pop(context);
                _copyReservationId(reservation.id);
              },
            ),
            if (reservation.guestPhone != null)
              _buildActionTile(
                icon: Icons.message,
                title: 'إرسال رسالة نصية',
                subtitle: 'إرسال رسالة للعميل',
                onTap: () {
                  Navigator.pop(context);
                  _sendSMS(reservation.guestPhone!);
                },
              ),
            _buildActionTile(
              icon: Icons.calendar_today,
              title: 'إضافة للتقويم',
              subtitle: 'إضافة تواريخ الحجز للتقويم',
              onTap: () {
                Navigator.pop(context);
                _addToCalendar(reservation);
              },
            ),
            if (reservation.item != null)
              _buildActionTile(
                icon: Icons.location_on,
                title: 'عرض الموقع',
                subtitle: 'عرض موقع العقار على الخريطة',
                onTap: () {
                  Navigator.pop(context);
                  _showPropertyLocation(reservation.item!);
                },
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.yellow.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: AppColors.yellow),
      ),
      title: Text(title, style: AppTextStyles.font14SemiBold),
      subtitle: Text(subtitle, style: AppTextStyles.font12Regular),
      onTap: onTap,
    );
  }

  void _shareReservationDetails(ReservationModel reservation) {
    final details = '''
تفاصيل الحجز #${reservation.id}

العقار: ${reservation.item?.title ?? 'غير محدد'}
العميل: ${reservation.guestName ?? 'غير محدد'}
${reservation.guestPhone != null ? 'الهاتف: ${reservation.guestPhone}' : ''}

تاريخ الوصول: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.parse(reservation.reservationFrom))}
تاريخ المغادرة: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.parse(reservation.reservationTo))}
عدد الليالي: ${reservation.durationInDays}

المبلغ الإجمالي: ${reservation.totalAmount.toStringAsFixed(0)} ر.س
الحالة: ${reservation.confirmed ? 'مؤكدة' : 'في الانتظار'}

تطبيق نقطة التجمع
    ''';

    Share.share(details);
  }

  void _copyReservationId(int reservationId) {
    Clipboard.setData(ClipboardData(text: reservationId.toString()));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم نسخ رقم الحجز')),
    );
  }

  Future<void> _sendSMS(String phoneNumber) async {
    final uri = Uri(scheme: 'sms', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن إرسال الرسالة')),
        );
      }
    }
  }

  void _addToCalendar(ReservationModel reservation) {
    // TODO: Implement calendar integration
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة ميزة التقويم قريباً')),
    );
  }

  void _showPropertyLocation(PropertyItemModel property) {
    // TODO: Implement map view for property location
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم إضافة عرض الموقع قريباً')),
    );
  }
}
