import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/services/property_edit_service.dart';

part 'property_creation_state.dart';

class PropertyCreationCubit extends Cubit<PropertyCreationState> {
  final PropertiesApiService _propertiesApiService;
  final PropertyEditService? _propertyEditService;

  // Rate limiting for facilities API calls
  DateTime? _lastFacilitiesCall;
  int? _lastCategoryId;
  static const Duration _facilitiesCallCooldown = Duration(seconds: 30);

  PropertyCreationCubit(this._propertiesApiService, [this._propertyEditService]) : super(PropertyCreationInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(PropertyCreationState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Load initial data (categories, facilities, property types, and cancellation policies)
  Future<void> loadInitialData() async {
    // Don't emit loading state to allow form to be displayed while data loads
    try {
      debugPrint('PropertyCreationCubit: Starting to load initial data...');

      // Load data one by one to identify which one is failing
      List<ServiceCategory> categories = [];
      List<FacilityModel> facilities = [];
      List<PropertyTypeModel> propertyTypes = [];
      List<CancellationPolicyModel> cancellationPolicies = [];

      try {
        debugPrint('Loading categories...');
        categories = await _propertiesApiService.getServiceCategories();
        debugPrint('✅ Categories loaded: ${categories.length}');
      } catch (e) {
        debugPrint('❌ Error loading categories: $e');
        // Add fallback mock data for testing
        categories = _getMockCategories();
        debugPrint('🔄 Using mock categories: ${categories.length}');
      }

      try {
        debugPrint('Loading facilities...');
        facilities = await _propertiesApiService.getFacilities();
        debugPrint('✅ Facilities loaded: ${facilities.length}');
      } catch (e) {
        debugPrint('❌ Error loading facilities: $e');
        // Add fallback mock data for testing
        facilities = _getMockFacilities();
        debugPrint('🔄 Using mock facilities: ${facilities.length}');
      }

      try {
        debugPrint('Loading property types...');
        propertyTypes = await _propertiesApiService.getPropertyTypes();
        debugPrint('✅ Property types loaded: ${propertyTypes.length}');
      } catch (e) {
        debugPrint('❌ Error loading property types: $e');
        // Add fallback mock data for testing
        propertyTypes = _getMockPropertyTypes();
        debugPrint('🔄 Using mock property types: ${propertyTypes.length}');
      }

      try {
        debugPrint('Loading cancellation policies...');
        cancellationPolicies = await _propertiesApiService.getCancellationPolicies();
        debugPrint('✅ Cancellation policies loaded: ${cancellationPolicies.length}');
      } catch (e) {
        debugPrint('❌ Error loading cancellation policies: $e');
        // Add fallback mock data for testing
        cancellationPolicies = _getMockCancellationPolicies();
        debugPrint('🔄 Using mock cancellation policies: ${cancellationPolicies.length}');
      }

      debugPrint('PropertyCreationCubit: Data loading completed');
      debugPrint('Final counts - Categories: ${categories.length}, Facilities: ${facilities.length}, Property Types: ${propertyTypes.length}, Cancellation Policies: ${cancellationPolicies.length}');

      _safeEmit(PropertyCreationDataLoaded(
        categories: categories,
        facilities: facilities,
        propertyTypes: propertyTypes,
        cancellationPolicies: cancellationPolicies,
        draftSaved: false, // Reset draft saved flag when loading fresh data
      ));
    } catch (e) {
      debugPrint('PropertyCreationCubit: Critical error loading initial data: $e');
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Load facilities filtered by service category
  Future<void> loadFacilitiesByCategory(int? serviceCategoryId) async {
    try {
      debugPrint('PropertyCreationCubit: Loading facilities for category: $serviceCategoryId');

      // Rate limiting check
      final now = DateTime.now();
      if (_lastFacilitiesCall != null &&
          _lastCategoryId == serviceCategoryId &&
          now.difference(_lastFacilitiesCall!).inSeconds < _facilitiesCallCooldown.inSeconds) {
        debugPrint('⏸️ Facilities API call skipped: rate limited (${now.difference(_lastFacilitiesCall!).inSeconds}s since last call)');
        return;
      }

      final currentState = state;

      // Allow loading facilities in both PropertyCreationDataLoaded and PropertyDraftSaved states
      PropertyCreationDataLoaded? dataLoadedState;
      if (currentState is PropertyCreationDataLoaded) {
        dataLoadedState = currentState;
      } else if (currentState is PropertyDraftSaved) {
        // If we're in draft saved state, we need to reload initial data first
        debugPrint('🔄 State is PropertyDraftSaved, reloading initial data...');
        await loadInitialData();
        final newState = state;
        if (newState is PropertyCreationDataLoaded) {
          dataLoadedState = newState;
        }
      }

      if (dataLoadedState == null) {
        debugPrint('❌ Cannot load facilities - invalid state: ${currentState.runtimeType}');
        return;
      }

      // Update rate limiting tracking
      _lastFacilitiesCall = now;
      _lastCategoryId = serviceCategoryId;

      Map<String, List<FacilityModel>>? groupedFacilities;
      List<FacilityModel> facilities;

      if (serviceCategoryId != null) {
        // Load grouped facilities for the specific category (single API call)
        groupedFacilities = await _propertiesApiService.getFacilitiesGroupedByCategory(
          serviceCategoryId: serviceCategoryId,
        );
        // Extract flat list from grouped data to avoid second API call
        facilities = groupedFacilities.values.expand((list) => list).toList();
        debugPrint('✅ Category facilities loaded: ${facilities.length} total, ${groupedFacilities.length} categories');
      } else {
        // Load all facilities (single API call)
        groupedFacilities = await _propertiesApiService.getFacilitiesGroupedByCategory();
        // Extract flat list from grouped data to avoid second API call
        facilities = groupedFacilities.values.expand((list) => list).toList();
        debugPrint('✅ All facilities loaded: ${facilities.length} total, ${groupedFacilities.length} categories');
      }

      _safeEmit(dataLoadedState.copyWith(
        facilities: facilities,
        groupedFacilities: groupedFacilities,
      ));
    } catch (e) {
      debugPrint('❌ Error loading facilities by category: $e');
      // Don't emit error state, just keep current facilities
    }
  }

  /// Create a new property
  Future<void> createProperty({
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
    File? tourismPermitDocument,
  }) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final property = await _propertiesApiService.createProperty(
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        address: propertyData['address'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        tourismPermitNumber: propertyData['tourism_permit_number'],
        propertyTypeId: propertyData['property_type_id'],
        shortTermPolicyId: propertyData['short_term_policy_id'],
        longTermPolicyId: propertyData['long_term_policy_id'],
        requiresConfirmation: propertyData['requires_confirmation'],
        facilityIds: propertyData['facilities'],
        mainImage: mainImage,
        video: video,
        galleryImages: galleryImages,
        tourismPermitDocument: tourismPermitDocument,
      );

      _safeEmit(PropertyCreationSuccess(property));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Update an existing property
  Future<void> updateProperty({
    required int propertyId,
    required Map<String, dynamic> propertyData,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
    List<String>? imagesToDelete,
  }) async {
    _safeEmit(PropertyCreationLoading());

    try {
      debugPrint('🔄 PropertyCreationCubit: Starting property update');
      debugPrint('📋 Property data: $propertyData');
      debugPrint('🔍 Facilities in property data: ${propertyData['facilities']} (type: ${propertyData['facilities'].runtimeType})');
      debugPrint('🖼️ Main image: ${mainImage != null ? 'Yes' : 'No'}');
      debugPrint('🎥 Video: ${video != null ? 'Yes' : 'No'}');
      debugPrint('🖼️ Gallery images: ${galleryImages?.length ?? 0}');
      debugPrint('🗑️ Images to delete: ${imagesToDelete?.length ?? 0}');
      debugPrint('🏷️ About to call API with facilityIds: ${propertyData['facilities']} (type: ${propertyData['facilities'].runtimeType})');

      final property = await _propertiesApiService.updateProperty(
        propertyId: propertyId,
        title: propertyData['title'],
        content: propertyData['content'],
        serviceCategoryId: propertyData['service_category_id'],
        price: propertyData['price'],
        weekendPrice: propertyData['weekend_price'],
        weekPrice: propertyData['week_price'],
        monthPrice: propertyData['month_price'],
        lat: propertyData['lat'],
        lon: propertyData['lon'],
        address: propertyData['address'],
        noGuests: propertyData['no_guests'],
        beds: propertyData['beds'],
        baths: propertyData['baths'],
        bookingRules: propertyData['booking_rules'],
        cancelationRules: propertyData['cancelation_rules'],
        tourismPermit: propertyData['tourism_permit'],
        propertyTypeId: propertyData['property_type_id'],
        shortTermPolicyId: propertyData['short_term_policy_id'],
        longTermPolicyId: propertyData['long_term_policy_id'],
        requiresConfirmation: propertyData['requires_confirmation'],
        facilityIds: propertyData['facilities'],
        imagesToDelete: imagesToDelete,
        mainImage: mainImage,
        video: video,
        galleryImages: galleryImages,
      );

      debugPrint('✅ PropertyCreationCubit: Property updated successfully');
      debugPrint('📋 Updated property: ${property.title} (ID: ${property.id})');
      _safeEmit(PropertyCreationSuccess(property));
    } catch (e) {
      debugPrint('❌ PropertyCreationCubit: Update failed with error: $e');
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Upload gallery images for a property
  Future<void> uploadGalleryImages(int propertyId, List<File> images) async {
    _safeEmit(PropertyCreationLoading());

    try {
      final success = await _propertiesApiService.uploadGalleryImages(propertyId, images);

      if (success) {
        _safeEmit(PropertyGalleryUploaded());
      } else {
        _safeEmit(PropertyCreationError('Failed to upload gallery images'));
      }
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Load property data for editing
  Future<void> loadPropertyForEdit(int propertyId) async {
    _safeEmit(PropertyCreationLoading());
    try {
      // Load form data first
      await loadInitialData();

      // Then load property data
      final property = await (_propertyEditService?.getPropertyForEdit(propertyId) ??
          Future.value(PropertyEditService.generateMockPropertyData(propertyId)));

      // Get the current loaded state
      final currentState = state as PropertyCreationDataLoaded;

      _safeEmit(PropertyEditDataLoaded(
        property: _convertToPropertyItem(property),
        categories: currentState.categories,
        facilities: currentState.facilities,
        propertyTypes: currentState.propertyTypes,
        cancellationPolicies: currentState.cancellationPolicies,
      ));
    } catch (e) {
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Save property as draft
  Future<void> savePropertyAsDraft(int propertyId, Map<String, dynamic> propertyData) async {
    try {
      await _propertyEditService?.savePropertyAsDraft(propertyId, propertyData);

      // Preserve the current state data and indicate draft was saved
      if (state is PropertyCreationDataLoaded) {
        final currentState = state as PropertyCreationDataLoaded;
        _safeEmit(currentState.copyWith(draftSaved: true));
        debugPrint('✅ Draft saved successfully - preserving form data');
      } else {
        _safeEmit(PropertyDraftSaved());
      }
    } catch (e) {
      debugPrint('❌ Failed to save draft: $e');
      _safeEmit(PropertyCreationError(e.toString()));
    }
  }

  /// Load property draft
  Future<Map<String, dynamic>?> loadPropertyDraft() async {
    try {
      return await _propertyEditService?.loadPropertyDraft();
    } catch (e) {
      debugPrint('Failed to load draft: $e');
      return null;
    }
  }

  /// Clear property draft
  Future<void> clearDraft() async {
    try {
      await _propertyEditService?.clearPropertyDraft();
      debugPrint('Draft cleared successfully');
    } catch (e) {
      debugPrint('Failed to clear draft: $e');
    }
  }

  /// Initialize form for edit mode with existing data
  void initializeForEdit(MyListingModel propertyData) {
    // Load form data with edit mode flag
    if (state is PropertyCreationDataLoaded) {
      final currentState = state as PropertyCreationDataLoaded;
      _safeEmit(currentState.copyWith(
        isEditMode: true,
        existingProperty: _convertToPropertyItem(propertyData),
      ));
    }
  }

  /// Check if cubit is in edit mode
  bool get isEditMode {
    if (state is PropertyCreationDataLoaded) {
      return (state as PropertyCreationDataLoaded).isEditMode;
    }
    if (state is PropertyEditDataLoaded) {
      return true;
    }
    return false;
  }

  /// Get existing property data if in edit mode
  PropertyItemModel? get existingProperty {
    if (state is PropertyCreationDataLoaded) {
      return (state as PropertyCreationDataLoaded).existingProperty;
    }
    if (state is PropertyEditDataLoaded) {
      return (state as PropertyEditDataLoaded).property;
    }
    return null;
  }

  /// Reset to create mode
  void resetToCreateMode() {
    if (state is PropertyCreationDataLoaded) {
      final currentState = state as PropertyCreationDataLoaded;
      _safeEmit(currentState.copyWith(
        isEditMode: false,
        existingProperty: null,
      ));
    }
  }

  /// Convert MyListingModel to PropertyItemModel
  PropertyItemModel _convertToPropertyItem(MyListingModel listing) {
    return PropertyItemModel(
      id: listing.id,
      title: listing.title,
      content: listing.content,
      image: listing.mainImageUrl,
      price: listing.price,
      weekendPrice: listing.weekendPrice,
      weekPrice: listing.weeklyPrice,
      monthPrice: listing.monthlyPrice,
      lat: listing.lat,
      lon: listing.lon,
      active: listing.isActive,
      serviceCategoryId: listing.category.id,
      userId: 1, // Default user - not available in MyListingModel
      views: listing.views,
      rating: listing.rating ?? 0.0,
      noOfRates: listing.reviewCount,
      noGuests: listing.noGuests,
      beds: listing.beds,
      baths: listing.baths,
      createdAt: listing.createdAt.toIso8601String(),
    );
  }

  /// Mock data for testing when APIs fail
  List<ServiceCategory> _getMockCategories() {
    return [
      ServiceCategory(id: 1, title: 'Accommodation', icon: 'home', image: '', order: 1),
      ServiceCategory(id: 2, title: 'Food & Dining', icon: 'restaurant', image: '', order: 2),
      ServiceCategory(id: 3, title: 'Events', icon: 'event', image: '', order: 3),
    ];
  }

  List<FacilityModel> _getMockFacilities() {
    return [
      const FacilityModel(id: 1, title: 'WiFi', icon: 'wifi', order: 1),
      const FacilityModel(id: 2, title: 'Parking', icon: 'parking', order: 2),
      const FacilityModel(id: 3, title: 'Pool', icon: 'pool', order: 3),
      const FacilityModel(id: 4, title: 'Air Conditioning', icon: 'ac', order: 4),
    ];
  }

  List<PropertyTypeModel> _getMockPropertyTypes() {
    return [
      PropertyTypeModel(id: 1, title: 'Apartment', order: 1, createdAt: '2024-01-01'),
      PropertyTypeModel(id: 2, title: 'Villa', order: 2, createdAt: '2024-01-01'),
      PropertyTypeModel(id: 3, title: 'Hotel Room', order: 3, createdAt: '2024-01-01'),
    ];
  }

  List<CancellationPolicyModel> _getMockCancellationPolicies() {
    return [
      CancellationPolicyModel(
        id: 1,
        nameEn: 'Flexible',
        nameAr: 'مرن',
        name: 'Flexible',
        descriptionEn: 'Full refund 1 day prior to arrival',
        descriptionAr: 'استرداد كامل قبل يوم واحد من الوصول',
        description: 'Full refund 1 day prior to arrival',
        policyType: 'flexible',
        durationType: 'days',
        cancellationWindowHours: 24,
        refundPercentage: 100.0,
        serviceFeeRefundable: true,
        cleaningFeeRefundable: true,
        isActive: true,
        order: 1,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      ),
      CancellationPolicyModel(
        id: 2,
        nameEn: 'Moderate',
        nameAr: 'متوسط',
        name: 'Moderate',
        descriptionEn: 'Full refund 5 days prior to arrival',
        descriptionAr: 'استرداد كامل قبل 5 أيام من الوصول',
        description: 'Full refund 5 days prior to arrival',
        policyType: 'moderate',
        durationType: 'days',
        cancellationWindowHours: 120,
        refundPercentage: 100.0,
        serviceFeeRefundable: true,
        cleaningFeeRefundable: false,
        isActive: true,
        order: 2,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      ),
      CancellationPolicyModel(
        id: 3,
        nameEn: 'Strict',
        nameAr: 'صارم',
        name: 'Strict',
        descriptionEn: 'Full refund 14 days prior to arrival',
        descriptionAr: 'استرداد كامل قبل 14 يوم من الوصول',
        description: 'Full refund 14 days prior to arrival',
        policyType: 'strict',
        durationType: 'days',
        cancellationWindowHours: 336,
        refundPercentage: 100.0,
        serviceFeeRefundable: false,
        cleaningFeeRefundable: false,
        isActive: true,
        order: 3,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      ),
    ];
  }
}
