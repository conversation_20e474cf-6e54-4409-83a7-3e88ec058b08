import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';


/// Edit Property Wizard that follows the same pattern as CreatePropertyWizard
class EditPropertyWizard extends StatefulWidget {
  final MyListingModel propertyData;

  const EditPropertyWizard({
    super.key,
    required this.propertyData,
  });

  @override
  State<EditPropertyWizard> createState() => _EditPropertyWizardState();
}

class _EditPropertyWizardState extends State<EditPropertyWizard> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 7; // Same as create wizard

  // Form controllers - initialized from existing data
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _weekendPriceController = TextEditingController();
  final TextEditingController _weekPriceController = TextEditingController();
  final TextEditingController _monthPriceController = TextEditingController();
  final TextEditingController _guestsController = TextEditingController();
  final TextEditingController _bedsController = TextEditingController();
  final TextEditingController _bathsController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _bookingRulesController = TextEditingController();
  final TextEditingController _tourismPermitController = TextEditingController();

  // Form state
  int? _selectedCategory;
  int? _selectedPropertyType;
  int? _selectedShortTermPolicy;
  int? _selectedLongTermPolicy;
  bool _requiresConfirmation = false;
  List<int> _selectedFacilities = [];
  
  // Location
  double? _selectedLat;
  double? _selectedLon;
  
  // Files and images
  List<String> _existingImages = [];
  List<String> _imagesToDelete = [];
  String? _existingVideo;
  String? _existingTourismDocument;

  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeFormWithExistingData();
  }

  void _initializeFormWithExistingData() {
    // Initialize form controllers with existing property data
    _titleController.text = widget.propertyData.title;
    _descriptionController.text = widget.propertyData.content;
    _priceController.text = widget.propertyData.price.toString();
    _weekendPriceController.text = widget.propertyData.weekendPrice?.toString() ?? '';
    _weekPriceController.text = widget.propertyData.weeklyPrice?.toString() ?? '';
    _monthPriceController.text = widget.propertyData.monthlyPrice?.toString() ?? '';
    _guestsController.text = widget.propertyData.noGuests.toString();
    _bedsController.text = widget.propertyData.beds.toString();
    _bathsController.text = widget.propertyData.baths.toString();
    _addressController.text = widget.propertyData.address ?? '';
    
    // Initialize selections
    _selectedCategory = widget.propertyData.category.id;
    _selectedPropertyType = widget.propertyData.propertyType?.id;
    
    // Initialize location
    _selectedLat = widget.propertyData.lat;
    _selectedLon = widget.propertyData.lon;
    
    // Initialize images
    _existingImages = List.from(widget.propertyData.galleryImages);
    if (widget.propertyData.mainImageUrl != null) {
      _existingImages.insert(0, widget.propertyData.mainImageUrl!);
    }

    // Initialize the cubit for edit mode
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<PropertyCreationCubit>();
      cubit.initializeForEdit(widget.propertyData);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _weekendPriceController.dispose();
    _weekPriceController.dispose();
    _monthPriceController.dispose();
    _guestsController.dispose();
    _bedsController.dispose();
    _bathsController.dispose();
    _addressController.dispose();
    _bookingRulesController.dispose();
    _tourismPermitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocConsumer<PropertyCreationCubit, PropertyCreationState>(
      listener: (context, state) {
        if (state is PropertyCreationSuccess) {
          _showSuccessDialog(s);
        } else if (state is PropertyCreationError) {
          _showErrorSnackBar(state.message);
          setState(() => _isSubmitting = false);
        } else if (state is PropertyCreationLoading) {
          setState(() => _isSubmitting = true);
        }
      },
      builder: (context, state) {
        final theme = Theme.of(context);

        return Scaffold(
          backgroundColor: theme.scaffoldBackgroundColor,
          resizeToAvoidBottomInset: true,
          extendBody: false,
          appBar: AppBar(
            title: Text(
              'Edit Property',
              style: theme.textTheme.titleLarge,
            ),
            backgroundColor: theme.appBarTheme.backgroundColor,
            foregroundColor: theme.appBarTheme.foregroundColor,
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: theme.appBarTheme.foregroundColor,
              ),
              onPressed: () {
                if (Navigator.of(context).canPop()) {
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                // Progress indicator
                _buildProgressIndicator(),

                // Form content
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildBasicInfoStep(state),
                      _buildCategoryStep(state),
                      _buildBookingRulesStep(state),
                      _buildDetailsStep(state),
                      _buildLocationStep(state),
                      _buildGalleryStep(state),
                      _buildReviewStep(),
                    ],
                  ),
                ),

                // Navigation buttons
                _buildNavigationButtons(state),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressIndicator() {
    final theme = Theme.of(context);
    final isRtl = Directionality.of(context) == TextDirection.rtl;
    final stepValidationStatus = _getStepValidationStatus();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Step indicators with icons
          Row(
            children: List.generate(_totalSteps, (index) {
              final isCompleted = index < _currentStep;
              final isCurrent = index == _currentStep;
              final isValid = stepValidationStatus[index] ?? false;
              
              return Expanded(
                child: GestureDetector(
                  onTap: () => _goToStep(index),
                  child: Column(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isCompleted
                              ? const Color(0xFFFEC53A)
                              : isCurrent
                                  ? (isValid ? const Color(0xFFFEC53A) : Colors.orange)
                                  : Colors.grey[300],
                        ),
                        child: Icon(
                          _getStepIcon(index),
                          size: 16,
                          color: isCompleted || isCurrent ? Colors.black : Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStepTitle(index),
                        style: AppTextStyles.font10Regular.copyWith(
                          color: isCurrent ? const Color(0xFFFEC53A) : Colors.grey[600],
                          fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
          
          const SizedBox(height: 12),
          
          // Progress bar
          LinearProgressIndicator(
            value: (_currentStep + 1) / _totalSteps,
            backgroundColor: Colors.grey[300],
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFFEC53A)),
          ),
        ],
      ),
    );
  }

  IconData _getStepIcon(int step) {
    switch (step) {
      case 0: return Icons.info_outline;
      case 1: return Icons.category_outlined;
      case 2: return Icons.rule_outlined;
      case 3: return Icons.details_outlined;
      case 4: return Icons.location_on_outlined;
      case 5: return Icons.photo_library_outlined;
      case 6: return Icons.preview_outlined;
      default: return Icons.help_outline;
    }
  }

  String _getStepTitle(int step) {
    switch (step) {
      case 0: return 'المعلومات الأساسية';
      case 1: return 'الفئة';
      case 2: return 'القواعد';
      case 3: return 'التفاصيل';
      case 4: return 'الموقع';
      case 5: return 'المعرض';
      case 6: return 'المراجعة';
      default: return '';
    }
  }

  Map<int, bool> _getStepValidationStatus() {
    return {
      0: _titleController.text.trim().isNotEmpty &&
         _titleController.text.trim().length >= 3 &&
         _descriptionController.text.trim().isNotEmpty &&
         _descriptionController.text.trim().length >= 10 &&
         _priceController.text.trim().isNotEmpty &&
         (double.tryParse(_priceController.text) ?? 0) >= 50,

      1: _selectedCategory != null &&
         _selectedPropertyType != null &&
         (_selectedShortTermPolicy != null || _selectedLongTermPolicy != null),

      2: _tourismPermitController.text.trim().isNotEmpty &&
         _tourismPermitController.text.trim().length >= 5,

      3: _guestsController.text.trim().isNotEmpty &&
         _bedsController.text.trim().isNotEmpty &&
         _bathsController.text.trim().isNotEmpty,

      4: _selectedLat != null && _selectedLon != null,

      5: _existingImages.isNotEmpty,

      6: true, // Review step is always valid
    };
  }

  void _goToStep(int step) {
    if (step <= _currentStep || _validateStepsUpTo(step - 1)) {
      setState(() => _currentStep = step);
      _pageController.animateToPage(
        step,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateStepsUpTo(int step) {
    final validation = _getStepValidationStatus();
    for (int i = 0; i <= step; i++) {
      if (!(validation[i] ?? false)) {
        return false;
      }
    }
    return true;
  }

  Widget _buildBasicInfoStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.basicInformation,
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل المعلومات الأساسية للعقار',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Property Title
          TextFormField(
            controller: _titleController,
            decoration: InputDecoration(
              labelText: s.propertyTitle,
              hintText: s.propertyTitleHint,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.title),
            ),
            maxLength: 100,
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Property Description
          TextFormField(
            controller: _descriptionController,
            decoration: InputDecoration(
              labelText: s.propertyDescription,
              hintText: s.propertyDescriptionHint,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.description),
            ),
            maxLines: 4,
            maxLength: 500,
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Daily Price
          TextFormField(
            controller: _priceController,
            decoration: InputDecoration(
              labelText: s.dailyPrice,
              hintText: 'أدخل السعر اليومي',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.attach_money),
              suffixText: 'ر.س',
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Weekend Price (Optional)
          TextFormField(
            controller: _weekendPriceController,
            decoration: InputDecoration(
              labelText: s.weekendPrice,
              hintText: 'أدخل سعر نهاية الأسبوع',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.weekend),
              suffixText: 'ر.س',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),

          // Weekly Price (Optional)
          TextFormField(
            controller: _weekPriceController,
            decoration: InputDecoration(
              labelText: s.weeklyPrice,
              hintText: 'أدخل السعر الأسبوعي',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.calendar_view_week),
              suffixText: 'ر.س',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),

          // Monthly Price (Optional)
          TextFormField(
            controller: _monthPriceController,
            decoration: InputDecoration(
              labelText: s.monthlyPrice,
              hintText: 'أدخل السعر الشهري',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.calendar_view_month),
              suffixText: 'ر.س',
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.categoryAndType,
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'اختر فئة الخدمة ونوع العقار',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Service Category Selection
          if (state is PropertyCreationDataLoaded) ...[
            Text(
              'فئة الخدمة',
              style: AppTextStyles.font16SemiBold,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: state.categories.map((category) {
                final isSelected = _selectedCategory == category.id;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category.id;
                    });
                    // Load facilities for this category
                    context.read<PropertyCreationCubit>().loadFacilitiesByCategory(category.id);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[300]!,
                      ),
                    ),
                    child: Text(
                      category.title,
                      style: AppTextStyles.font14Medium.copyWith(
                        color: isSelected ? Colors.black : Colors.grey[700],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),

            // Property Type Selection
            Text(
              s.propertyType,
              style: AppTextStyles.font16SemiBold,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: state.propertyTypes.map((type) {
                final isSelected = _selectedPropertyType == type.id;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedPropertyType = type.id;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? const Color(0xFFFEC53A) : Colors.grey[300]!,
                      ),
                    ),
                    child: Text(
                      type.title,
                      style: AppTextStyles.font14Medium.copyWith(
                        color: isSelected ? Colors.black : Colors.grey[700],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),

            // Cancellation Policy Selection
            _buildCancellationPolicySelection(state),
          ],
        ],
      ),
    );
  }

  Widget _buildBookingRulesStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'قواعد الحجز والسياحة',
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل قواعد الحجز ورقم التصريح السياحي',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Tourism Permit Number
          TextFormField(
            controller: _tourismPermitController,
            decoration: InputDecoration(
              labelText: s.tourismPermitNumber,
              hintText: 'أدخل رقم التصريح السياحي',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.verified_user),
            ),
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Booking Rules
          TextFormField(
            controller: _bookingRulesController,
            decoration: InputDecoration(
              labelText: s.bookingRules,
              hintText: 'أدخل قواعد الحجز والإلغاء',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.rule),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),

          // Requires Confirmation
          SwitchListTile(
            title: const Text('يتطلب تأكيد الحجز'),
            subtitle: const Text('هل تريد تأكيد الحجوزات يدوياً؟'),
            value: _requiresConfirmation,
            onChanged: (value) {
              setState(() {
                _requiresConfirmation = value;
              });
            },
            activeColor: const Color(0xFFFEC53A),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل العقار',
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل تفاصيل العقار والمرافق المتاحة',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Number of Guests
          TextFormField(
            controller: _guestsController,
            decoration: InputDecoration(
              labelText: s.numberOfGuests,
              hintText: 'عدد الضيوف المسموح',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.people),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Number of Beds
          TextFormField(
            controller: _bedsController,
            decoration: InputDecoration(
              labelText: 'عدد الأسرة',
              hintText: 'عدد الأسرة',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.bed),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 16),

          // Number of Bathrooms
          TextFormField(
            controller: _bathsController,
            decoration: InputDecoration(
              labelText: s.numberOfBathrooms,
              hintText: 'عدد دورات المياه',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.bathtub),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) => setState(() {}),
          ),
          const SizedBox(height: 24),

          // Facilities
          if (state is PropertyCreationDataLoaded && state.facilities.isNotEmpty) ...[
            Text(
              s.facilities,
              style: AppTextStyles.font16SemiBold,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: state.facilities.map((facility) {
                final isSelected = _selectedFacilities.contains(facility.id);
                return FilterChip(
                  label: Text(facility.title),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedFacilities.add(facility.id);
                      } else {
                        _selectedFacilities.remove(facility.id);
                      }
                    });
                  },
                  selectedColor: const Color(0xFFFEC53A),
                  checkmarkColor: Colors.black,
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'موقع العقار',
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'حدد موقع العقار على الخريطة',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Address
          TextFormField(
            controller: _addressController,
            decoration: InputDecoration(
              labelText: s.address,
              hintText: 'أدخل عنوان العقار',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixIcon: const Icon(Icons.location_on),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),

          // Map placeholder
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.map, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 8),
                Text(
                  'اضغط لتحديد الموقع',
                  style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
                ),
                if (_selectedLat != null && _selectedLon != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    '${_selectedLat!.toStringAsFixed(6)}, ${_selectedLon!.toStringAsFixed(6)}',
                    style: AppTextStyles.font12Regular.copyWith(color: Colors.grey[500]),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(PropertyCreationState state) {
    final s = S.of(context);
    final isLastStep = _currentStep == _totalSteps - 1;
    final canProceed = _getStepValidationStatus()[_currentStep] ?? false;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _isSubmitting ? null : _previousStep,
                icon: const Icon(Icons.arrow_back),
                label: Text(s.previous),
              ),
            ),

          if (_currentStep > 0) const SizedBox(width: 16),

          // Next/Submit button
          Expanded(
            flex: _currentStep == 0 ? 1 : 1,
            child: ElevatedButton.icon(
              onPressed: _isSubmitting || !canProceed ? null : (isLastStep ? _submitForm : _nextStep),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFEC53A),
                foregroundColor: Colors.black,
              ),
              icon: _isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Icon(isLastStep ? Icons.check : Icons.arrow_forward),
              label: Text(isLastStep ? s.updateProperty : s.next),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGalleryStep(PropertyCreationState state) {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معرض العقار',
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'أضف صور ومقاطع فيديو للعقار',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Images Section
          Text(
            s.propertyImages,
            style: AppTextStyles.font16SemiBold,
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _getAllImages().length + 1,
              itemBuilder: (context, index) {
                final allImages = _getAllImages();
                if (index == allImages.length) {
                  return _buildAddImageButton();
                }
                return _buildImageItem(allImages[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    final s = S.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مراجعة وإرسال',
            style: AppTextStyles.font18Bold,
          ),
          const SizedBox(height: 8),
          Text(
            'راجع بيانات العقار قبل التحديث',
            style: AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // Property Summary
          _buildPropertySummary(),
        ],
      ),
    );
  }

  Widget _buildPropertySummary() {
    final s = S.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص العقار',
              style: AppTextStyles.font16SemiBold,
            ),
            const SizedBox(height: 16),

            _buildSummaryRow(s.propertyTitle, _titleController.text),
            _buildSummaryRow(s.dailyPrice, '${_priceController.text} ر.س'),
            _buildSummaryRow(s.numberOfGuests, _guestsController.text),
            _buildSummaryRow('عدد الأسرة', _bedsController.text),
            _buildSummaryRow('عدد دورات المياه', _bathsController.text),
            _buildSummaryRow(s.address, _addressController.text),

            if (_existingImages.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${s.propertyImages}: ${_existingImages.length}',
                style: AppTextStyles.font14Regular,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.font14Medium,
            ),
          ),
          Expanded(
            child: Text(
              value.isNotEmpty ? value : '-',
              style: AppTextStyles.font14Regular,
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getAllImages() {
    final allImages = <String>[];

    // Add existing images that are not marked for deletion
    for (final image in _existingImages) {
      if (!_imagesToDelete.contains(image)) {
        allImages.add(image);
      }
    }

    return allImages;
  }

  Widget _buildImageItem(String imageUrl, int index) {
    final isMarkedForDeletion = _imagesToDelete.contains(imageUrl);

    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        image: DecorationImage(
          image: NetworkImage(imageUrl),
          fit: BoxFit.cover,
          colorFilter: isMarkedForDeletion
              ? ColorFilter.mode(Colors.red.withValues(alpha: 0.5), BlendMode.color)
              : null,
        ),
      ),
      child: Stack(
        children: [
          // Deletion overlay
          if (isMarkedForDeletion)
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.red.withValues(alpha: 0.3),
              ),
              child: const Center(
                child: Icon(
                  Icons.delete,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),

          // Remove button
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => _removeImage(imageUrl),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: isMarkedForDeletion ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isMarkedForDeletion ? Icons.undo : Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _addNewImage,
      child: Container(
        width: 120,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, size: 32, color: Colors.grey),
            SizedBox(height: 8),
            Text('إضافة صورة', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  void _removeImage(String imageUrl) {
    setState(() {
      if (_imagesToDelete.contains(imageUrl)) {
        _imagesToDelete.remove(imageUrl);
      } else {
        _imagesToDelete.add(imageUrl);
      }
    });
  }

  void _addNewImage() {
    // TODO: Implement image picker
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Image picker to be implemented'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() => _currentStep++);
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _submitForm() {
    final propertyData = _getFormData();
    context.read<PropertyCreationCubit>().updateProperty(
      propertyId: widget.propertyData.id,
      propertyData: propertyData,
      imagesToDelete: _imagesToDelete,
    );
  }

  Map<String, dynamic> _getFormData() {
    return {
      'title': _titleController.text.trim(),
      'content': _descriptionController.text.trim(),
      'price': double.tryParse(_priceController.text) ?? 0.0,
      'weekend_price': _weekendPriceController.text.isNotEmpty
          ? double.tryParse(_weekendPriceController.text)
          : null,
      'weekly_price': _weekPriceController.text.isNotEmpty
          ? double.tryParse(_weekPriceController.text)
          : null,
      'monthly_price': _monthPriceController.text.isNotEmpty
          ? double.tryParse(_monthPriceController.text)
          : null,
      'address': _addressController.text.trim(),
      'lat': _selectedLat,
      'lon': _selectedLon,
      'service_category_id': _selectedCategory,
      'property_type_id': _selectedPropertyType,
      'no_guests': int.tryParse(_guestsController.text) ?? 1,
      'beds': int.tryParse(_bedsController.text) ?? 1,
      'baths': int.tryParse(_bathsController.text) ?? 1,
      'booking_rules': _bookingRulesController.text.trim(),
      'tourism_permit': _tourismPermitController.text.trim(),
      'requires_confirmation': _requiresConfirmation,
      'facilities': _selectedFacilities,
      'images_to_delete': _imagesToDelete,
    };
  }

  void _showSuccessDialog(S s) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 32),
            const SizedBox(width: 12),
            Text('نجح'),
          ],
        ),
        content: Text('تم تحديث العقار بنجاح'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(true); // Return to listings with refresh
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFEC53A),
              foregroundColor: Colors.black,
            ),
            child: Text(s.ok),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Widget _buildCancellationPolicySelection(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get cancellation policies from API data or use empty list if not loaded
    final policies = state is PropertyCreationDataLoaded
        ? state.cancellationPolicies
        : <CancellationPolicyModel>[];

    // Separate policies by duration type
    final shortTermPolicies = policies.where((p) => p.durationType == 'short' || p.durationType == 'both').toList();
    final longTermPolicies = policies.where((p) => p.durationType == 'long' || p.durationType == 'both').toList();

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.policy, color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.cancellationPolicy,
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Policy list
            if (policies.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        Icon(Icons.error_outline, color: theme.colorScheme.error, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load cancellation policies',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context.read<PropertyCreationCubit>().loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading cancellation policies...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else ...[
              // Short-term policies section
              if (shortTermPolicies.isNotEmpty) ...[
                _buildPolicySectionHeader(
                  'Short-term Policies (≤28 days)',
                  'سياسات قصيرة المدى (≤28 يوم)',
                  Icons.schedule,
                  theme,
                ),
                const SizedBox(height: 12),
                ...shortTermPolicies.map((policy) => _buildPolicyCard(policy, theme)),
                const SizedBox(height: 20),
              ],

              // Long-term policies section
              if (longTermPolicies.isNotEmpty) ...[
                _buildPolicySectionHeader(
                  'Long-term Policies (>28 days)',
                  'سياسات طويلة المدى (>28 يوم)',
                  Icons.event,
                  theme,
                ),
                const SizedBox(height: 12),
                ...longTermPolicies.map((policy) => _buildPolicyCard(policy, theme)),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPolicySectionHeader(String titleEn, String titleAr, IconData icon, ThemeData theme) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(icon, color: theme.primaryColor, size: 18),
          const SizedBox(width: 8),
          Text(
            isArabic ? titleAr : titleEn,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyCard(CancellationPolicyModel policy, ThemeData theme) {
    final isShortTerm = policy.durationType == 'short' || policy.durationType == 'both';
    final isLongTerm = policy.durationType == 'long' || policy.durationType == 'both';

    bool isSelected = false;
    if (isShortTerm && _selectedShortTermPolicy == policy.id) isSelected = true;
    if (isLongTerm && _selectedLongTermPolicy == policy.id) isSelected = true;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: () {
          setState(() {
            if (isShortTerm && isLongTerm) {
              // Policy applies to both - user can choose which slot to use
              if (_selectedShortTermPolicy == null) {
                _selectedShortTermPolicy = policy.id;
              } else if (_selectedLongTermPolicy == null) {
                _selectedLongTermPolicy = policy.id;
              } else {
                // Both slots filled, replace short term
                _selectedShortTermPolicy = policy.id;
              }
            } else if (isShortTerm) {
              _selectedShortTermPolicy = policy.id;
            } else if (isLongTerm) {
              _selectedLongTermPolicy = policy.id;
            }
          });
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? (theme.brightness == Brightness.dark
                    ? const Color(0xFFFFD234).withValues(alpha: 0.15) // Yellow background in dark mode
                    : theme.primaryColor.withValues(alpha: 0.1))
                : theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? (theme.brightness == Brightness.dark
                      ? const Color(0xFFFFD234) // Yellow border in dark mode
                      : theme.primaryColor)
                  : theme.dividerColor,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (theme.brightness == Brightness.dark
                          ? const Color(0xFFFFD234) // Yellow in dark mode
                          : theme.primaryColor)
                      : theme.colorScheme.outline,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.schedule,
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      policy.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234) // Yellow in dark mode
                                : theme.primaryColor)
                            : theme.colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      policy.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: theme.brightness == Brightness.dark
                        ? const Color(0xFFFFD234) // Yellow in dark mode
                        : theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.check,
                    color: theme.colorScheme.onPrimary,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
