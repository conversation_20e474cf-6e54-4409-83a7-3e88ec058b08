import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

import 'package:latlong2/latlong.dart';
import 'package:video_player/video_player.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/routing/routes_keys.dart';

import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/home/<USER>/Data Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';

import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/host/widgets/location_map_picker.dart';
import 'package:gather_point/feature/host/services/geocoding_service.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';

// Step configuration class
class StepConfig {
  final IconData icon;
  final String title;
  final String titleAr;

  const StepConfig({
    required this.icon,
    required this.title,
    required this.titleAr,
  });
}

/// Edit Property Wizard that follows the exact same pattern as CreatePropertyWizard
class EditPropertyWizard extends StatefulWidget {
  final MyListingModel propertyData;

  const EditPropertyWizard({
    super.key,
    required this.propertyData,
  });

  @override
  State<EditPropertyWizard> createState() => _EditPropertyWizardState();
}

class _EditPropertyWizardState extends State<EditPropertyWizard> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 6; // Removed tourism permit step

  // Step configuration with icons and titles
  List<StepConfig> get _stepConfigs => [
        StepConfig(
          icon: Icons.info_outline,
          title: 'Basic Info',
          titleAr: 'المعلومات الأساسية',
        ),
        StepConfig(
          icon: Icons.category_outlined,
          title: 'Category',
          titleAr: 'الفئة',
        ),
        StepConfig(
          icon: Icons.hotel_outlined,
          title: 'Property Details',
          titleAr: 'تفاصيل العقار',
        ),
        StepConfig(
          icon: Icons.location_on_outlined,
          title: 'Location',
          titleAr: 'الموقع',
        ),
        StepConfig(
          icon: Icons.photo_library_outlined,
          title: 'Media',
          titleAr: 'الوسائط',
        ),
        StepConfig(
          icon: Icons.rule_outlined,
          title: 'Rules',
          titleAr: 'القواعد',
        ),
      ];

  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _weekendPriceController = TextEditingController();
  final _weekPriceController = TextEditingController();
  final _monthPriceController = TextEditingController();
  final _guestsController = TextEditingController();
  final _bedsController = TextEditingController();
  final _bathsController = TextEditingController();
  final _bookingRulesController = TextEditingController();

  // Form data
  int? _selectedCategory;
  int? _selectedPropertyType;
  int? _selectedShortTermPolicy;
  int? _selectedLongTermPolicy;
  List<int> _selectedFacilities = [];
  bool _requiresConfirmation = false;

  // Media and location
  final List<File> _imageGallery = []; // New images to upload
  List<String> _existingImageUrls = []; // Existing images from server
  final List<String> _imagesToDelete = []; // Existing images marked for deletion
  File? _videoFile; // New video to upload
  LatLng? _selectedLocation;
  String? _locationAddress;
  final ImagePicker _picker = ImagePicker();
  VideoPlayerController? _videoController;

  // Form submission state
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeFormWithExistingData();

    // Add a delay to ensure the cubit is properly initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<PropertyCreationCubit>();
      debugPrint('InitState - Current cubit state: ${cubit.state.runtimeType}');

      // Test API services
      _testApiServices();

      // If still in initial state, try loading data again
      if (cubit.state is PropertyCreationInitial) {
        debugPrint('InitState - State is initial, loading data...');
        cubit.loadInitialData();
      }

      // Initialize the cubit for edit mode
      cubit.initializeForEdit(widget.propertyData);
    });
  }

  void _initializeFormWithExistingData() {
    // Initialize form controllers with existing property data
    _titleController.text = widget.propertyData.title;
    _descriptionController.text = widget.propertyData.content;
    _priceController.text = widget.propertyData.price.toString();
    _weekendPriceController.text =
        widget.propertyData.weekendPrice?.toString() ?? '';
    _weekPriceController.text =
        widget.propertyData.weeklyPrice?.toString() ?? '';
    _monthPriceController.text =
        widget.propertyData.monthlyPrice?.toString() ?? '';
    _guestsController.text = widget.propertyData.noGuests.toString();
    _bedsController.text = widget.propertyData.beds.toString();
    _bathsController.text = widget.propertyData.baths.toString();

    // Initialize selections
    _selectedCategory = widget.propertyData.category.id;
    _selectedPropertyType = widget.propertyData.propertyType?.id;

    // Initialize location
    if (widget.propertyData.lat != null && widget.propertyData.lon != null) {
      _selectedLocation =
          LatLng(widget.propertyData.lat!, widget.propertyData.lon!);
      _locationAddress = widget.propertyData.address;
    }

    // Initialize existing images - convert URLs to display in gallery
    // Note: For edit mode, we'll show existing images as network images
    // and only upload new images if user adds them
    _existingImageUrls = List<String>.from(widget.propertyData.galleryImages);

    debugPrint('✅ Initialized existing images: ${_existingImageUrls.length} images');
    debugPrint('📸 Existing image URLs: $_existingImageUrls');

    // Initialize facilities if available
    if (widget.propertyData.facilities.isNotEmpty) {
      try {
        _selectedFacilities =
            widget.propertyData.facilities.map((f) => int.parse(f)).toList();
        debugPrint('✅ Initialized facilities: $_selectedFacilities');
      } catch (e) {
        debugPrint('❌ Error parsing facilities: $e');
        _selectedFacilities = [];
      }
    }
  }



  Future<void> _testApiServices() async {
    try {
      debugPrint('Testing API services...');
      final propertiesService = getIt<PropertiesApiService>();

      // Test each service individually
      try {
        final categories = await propertiesService.getServiceCategories();
        debugPrint('Categories API test: SUCCESS - ${categories.length} items');
      } catch (e) {
        debugPrint('Categories API test: FAILED - $e');
      }

      try {
        final facilities = await propertiesService.getFacilities();
        debugPrint('Facilities API test: SUCCESS - ${facilities.length} items');
      } catch (e) {
        debugPrint('Facilities API test: FAILED - $e');
      }

      try {
        final propertyTypes = await propertiesService.getPropertyTypes();
        debugPrint(
            'Property Types API test: SUCCESS - ${propertyTypes.length} items');
      } catch (e) {
        debugPrint('Property Types API test: FAILED - $e');
      }

      try {
        final cancellationPolicies =
            await propertiesService.getCancellationPolicies();
        debugPrint(
            'Cancellation Policies API test: SUCCESS - ${cancellationPolicies.length} items');
      } catch (e) {
        debugPrint('Cancellation Policies API test: FAILED - $e');
      }
    } catch (e) {
      debugPrint('API services test failed: $e');
    }
  }

  void _populateFormFromProperty(PropertyItemModel property) {
    // We need to populate from the original MyListingModel data, not the converted PropertyItemModel
    // The PropertyItemModel conversion loses important data like images, facilities, etc.
    final originalProperty = widget.propertyData;

    setState(() {
      // Basic information
      _titleController.text = originalProperty.title;
      _descriptionController.text = originalProperty.content;
      _priceController.text = originalProperty.price.toString();
      _weekendPriceController.text = originalProperty.weekendPrice?.toString() ?? '';
      _weekPriceController.text = originalProperty.weeklyPrice?.toString() ?? '';
      _monthPriceController.text = originalProperty.monthlyPrice?.toString() ?? '';

      // Property details
      _guestsController.text = originalProperty.noGuests.toString();
      _bedsController.text = originalProperty.beds.toString();
      _bathsController.text = originalProperty.baths.toString();

      // Category and property type
      _selectedCategory = originalProperty.category.id;
      _selectedPropertyType = originalProperty.propertyType?.id;

      // Location
      if (originalProperty.lat != null && originalProperty.lon != null) {
        _selectedLocation = LatLng(originalProperty.lat!, originalProperty.lon!);
        _locationAddress = originalProperty.address;
      }

      // Images - Load existing images from server
      _existingImageUrls.clear();
      if (originalProperty.mainImageUrl != null && originalProperty.mainImageUrl!.isNotEmpty) {
        _existingImageUrls.add(originalProperty.mainImageUrl!);
      }
      _existingImageUrls.addAll(originalProperty.galleryImages);

      // Facilities - Convert facility names to IDs (will be matched when facilities are loaded)
      _selectedFacilities.clear();

      // Booking rules - if available in the original property data
      // Note: MyListingModel doesn't have booking rules field, so we'll leave it empty for now
      _bookingRulesController.text = '';

      debugPrint('✅ Form populated with existing property data');
      debugPrint('📸 Loaded ${_existingImageUrls.length} existing images');
      debugPrint('🏠 Property type ID: $_selectedPropertyType');
      debugPrint('📍 Location: $_selectedLocation');
      debugPrint('🏷️ Facilities to match: ${originalProperty.facilities}');
    });

    // Match facilities after form population
    _matchFacilitiesWithLoadedData();
  }

  void _matchFacilitiesWithLoadedData() {
    // This will be called after facilities are loaded to match facility names with IDs
    final cubit = context.read<PropertyCreationCubit>();
    final state = cubit.state;

    if (state is PropertyCreationDataLoaded && state.facilities.isNotEmpty) {
      final originalProperty = widget.propertyData;
      _selectedFacilities.clear();

      for (String facilityName in originalProperty.facilities) {
        // Find facility by title (case-insensitive)
        final facility = state.facilities.firstWhere(
          (f) => f.title.toLowerCase() == facilityName.toLowerCase(),
          orElse: () => const FacilityModel(
            id: -1,
            title: '',
            order: 0
          ),
        );

        if (facility.id != -1) {
          _selectedFacilities.add(facility.id);
        }
      }

      debugPrint('🏷️ Matched ${_selectedFacilities.length} facilities: $_selectedFacilities');
      setState(() {}); // Refresh UI with matched facilities
    }
  }



  @override
  void dispose() {
    _pageController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _weekendPriceController.dispose();
    _weekPriceController.dispose();
    _monthPriceController.dispose();
    _guestsController.dispose();
    _bedsController.dispose();
    _bathsController.dispose();
    _videoController?.dispose();
    super.dispose();
  }







  void _nextStep() {
    // Dismiss keyboard before navigation
    FocusScope.of(context).unfocus();

    if (_validateCurrentStep()) {
      if (_currentStep < _totalSteps - 1) {
        setState(() => _currentStep++);
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );

      }
    } else {
      _showValidationErrors();
    }
  }

  // Silent validation for build-time checks (no snack bars)
  bool _isCurrentStepValid() {
    switch (_currentStep) {
      case 0: // Basic Information
        if (_titleController.text.trim().isEmpty) return false;
        if (_titleController.text.trim().length < 3) return false;
        if (_descriptionController.text.trim().isEmpty) return false;
        if (_descriptionController.text.trim().length < 10) return false;
        if (_priceController.text.trim().isEmpty) return false;
        final price = double.tryParse(_priceController.text);
        if (price == null || price <= 0) return false;
        if (price < 50) return false;
        break;

      case 1: // Category & Type
        if (_selectedCategory == null) return false;
        if (_selectedPropertyType == null) return false;
        if (_selectedShortTermPolicy == null && _selectedLongTermPolicy == null)
          return false;
        break;

      case 2: // Property Details
        if (_guestsController.text.trim().isEmpty) return false;
        final guests = int.tryParse(_guestsController.text);
        if (guests == null || guests <= 0 || guests > 20) return false;
        if (_bedsController.text.trim().isEmpty) return false;
        final beds = int.tryParse(_bedsController.text);
        if (beds == null || beds <= 0 || beds > 10) return false;
        if (_bathsController.text.trim().isEmpty) return false;
        final baths = int.tryParse(_bathsController.text);
        if (baths == null || baths <= 0 || baths > 10) return false;
        if (_selectedFacilities.isEmpty) return false;
        break;

      case 3: // Location
        if (_selectedLocation == null) return false;
        break;

      case 4: // Gallery
        if (_imageGallery.isEmpty) return false;
        if (_imageGallery.length < 2) return false;
        break;
    }
    return true;
  }

  // Validation with user feedback (shows snack bars)
  bool _validateCurrentStep() {
    final s = S.of(context);

    switch (_currentStep) {
      case 0: // Basic Information
        if (_titleController.text.trim().isEmpty) {
          _showErrorSnackBar(s.propertyTitleRequired);
          return false;
        }
        if (_titleController.text.trim().length < 3) {
          _showErrorSnackBar(s.propertyTitleTooShort);
          return false;
        }
        if (_descriptionController.text.trim().isEmpty) {
          _showErrorSnackBar(s.propertyDescriptionRequired);
          return false;
        }
        if (_descriptionController.text.trim().length < 10) {
          _showErrorSnackBar(s.propertyDescriptionTooShort);
          return false;
        }
        if (_priceController.text.trim().isEmpty) {
          _showErrorSnackBar(s.priceRequired);
          return false;
        }
        final price = double.tryParse(_priceController.text);
        if (price == null || price <= 0) {
          _showErrorSnackBar(s.priceInvalid);
          return false;
        }
        if (price < 50) {
          _showErrorSnackBar(s.priceMinimum);
          return false;
        }
        break;

      case 1: // Category & Type
        if (_selectedCategory == null) {
          _showErrorSnackBar(s.categoryRequired);
          return false;
        }
        if (_selectedPropertyType == null) {
          _showErrorSnackBar(s.propertyTypeRequired);
          return false;
        }
        if (_selectedShortTermPolicy == null &&
            _selectedLongTermPolicy == null) {
          _showErrorSnackBar('Please select at least one cancellation policy');
          return false;
        }
        break;

      case 2: // Property Details
        if (_guestsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.guestsRequired);
          return false;
        }
        final guests = int.tryParse(_guestsController.text);
        if (guests == null || guests <= 0 || guests > 20) {
          _showErrorSnackBar(s.guestsInvalid);
          return false;
        }
        if (_bedsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.bedsRequired);
          return false;
        }
        final beds = int.tryParse(_bedsController.text);
        if (beds == null || beds <= 0 || beds > 10) {
          _showErrorSnackBar(s.bedsInvalid);
          return false;
        }
        if (_bathsController.text.trim().isEmpty) {
          _showErrorSnackBar(s.bathsRequired);
          return false;
        }
        final baths = int.tryParse(_bathsController.text);
        if (baths == null || baths <= 0 || baths > 10) {
          _showErrorSnackBar(s.bathsInvalid);
          return false;
        }
        if (_selectedFacilities.isEmpty) {
          _showErrorSnackBar(s.facilitiesRequired);
          return false;
        }
        // Debug: Print selected facilities
        debugPrint(
            '✅ Facilities validation passed. Selected: $_selectedFacilities');
        break;

      case 4: // Location
        if (_selectedLocation == null) {
          _showErrorSnackBar(s.locationRequired);
          return false;
        }
        break;

      case 5: // Gallery
        int totalImages = _getTotalImageCount();
        debugPrint(
            '🖼️ Gallery validation - Total images: $totalImages (existing: ${_existingImageUrls.length}, new: ${_imageGallery.length})');
        if (totalImages == 0) {
          debugPrint('❌ Gallery validation failed - No images');
          _showErrorSnackBar(s.imagesRequired);
          return false;
        }
        if (totalImages < 2) {
          debugPrint('❌ Gallery validation failed - Less than 2 images');
          _showErrorSnackBar(s.imagesMinimum);
          return false;
        }
        debugPrint('✅ Gallery validation passed');
        break;
    }

    return true;
  }

  // Comprehensive validation for all steps
  bool _validateAllSteps() {
    // Step 0: Basic Information
    if (_titleController.text.trim().isEmpty ||
        _titleController.text.trim().length < 3) {
      return false;
    }
    if (_descriptionController.text.trim().isEmpty ||
        _descriptionController.text.trim().length < 10) return false;
    final price = double.tryParse(_priceController.text);
    if (price == null || price < 50) return false;

    // Step 1: Category & Type
    if (_selectedCategory == null) return false;
    if (_selectedPropertyType == null) return false;
    if (_selectedShortTermPolicy == null && _selectedLongTermPolicy == null) {
      return false;
    }

    // Step 2: Property Details
    final guests = int.tryParse(_guestsController.text);
    if (guests == null || guests <= 0 || guests > 20) return false;
    final beds = int.tryParse(_bedsController.text);
    if (beds == null || beds <= 0 || beds > 10) return false;
    final baths = int.tryParse(_bathsController.text);
    if (baths == null || baths <= 0 || baths > 10) return false;
    if (_selectedFacilities.isEmpty) return false;

    // Step 4: Location
    if (_selectedLocation == null) return false;

    // Step 5: Gallery - check total images for edit mode
    if (_getTotalImageCount() < 2) return false;

    return true;
  }

  // Get validation status for each step
  Map<int, bool> _getStepValidationStatus() {
    return {
      0: _titleController.text.trim().isNotEmpty &&
          _titleController.text.trim().length >= 3 &&
          _descriptionController.text.trim().isNotEmpty &&
          _descriptionController.text.trim().length >= 10 &&
          _priceController.text.trim().isNotEmpty &&
          (double.tryParse(_priceController.text) ?? 0) >= 50,

      1: _selectedCategory != null &&
          _selectedPropertyType != null &&
          (_selectedShortTermPolicy != null || _selectedLongTermPolicy != null),

      2: _guestsController.text.trim().isNotEmpty &&
          (int.tryParse(_guestsController.text) ?? 0) > 0 &&
          (int.tryParse(_guestsController.text) ?? 0) <= 20 &&
          _bedsController.text.trim().isNotEmpty &&
          (int.tryParse(_bedsController.text) ?? 0) > 0 &&
          (int.tryParse(_bedsController.text) ?? 0) <= 10 &&
          _bathsController.text.trim().isNotEmpty &&
          (int.tryParse(_bathsController.text) ?? 0) > 0 &&
          (int.tryParse(_bathsController.text) ?? 0) <= 10 &&
          _selectedFacilities.isNotEmpty,

      3: _selectedLocation != null,

      4: _getTotalImageCount() >= 3,

      5: true, // Booking rules step is optional
    };
  }

  void _showErrorSnackBar(String message) {
    final theme = Theme.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
                child:
                    Text(message, style: const TextStyle(color: Colors.white))),
          ],
        ),
        backgroundColor: theme.colorScheme.error,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(bottom: 100, left: 16, right: 16),
      ),
    );
    HapticFeedback.lightImpact();
  }

  void _showValidationErrors() {
    // Additional validation error handling if needed
  }



  void _previousStep() {
    // Dismiss keyboard before navigation
    FocusScope.of(context).unfocus();

    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Check if user can navigate to a specific step
  bool _canNavigateToStep(int targetStep) {
    // Allow navigation to any previous step or current step
    if (targetStep <= _currentStep) return true;

    // Allow navigation to next step only if current step is valid
    if (targetStep == _currentStep + 1) {
      return _isCurrentStepValid(); // Use silent validation during build
    }

    // Don't allow skipping multiple steps ahead
    return false;
  }

  // Navigate to a specific step
  void _navigateToStep(int targetStep) {
    if (!_canNavigateToStep(targetStep)) return;

    // Dismiss keyboard before navigation
    FocusScope.of(context).unfocus();



    setState(() => _currentStep = targetStep);
    _pageController.animateToPage(
      targetStep,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _submitForm() async {
    final s = S.of(context);

    // Prevent multiple submissions
    if (_isSubmitting) {
      debugPrint(
          '⚠️ Form submission already in progress, ignoring duplicate click');
      return;
    }

    if (!_validateCurrentStep()) {
      _showErrorSnackBar(s.validationFailed);
      return;
    }

    // Additional validation for required uploads before submission
    // For edit mode, check if we'll have at least one image after deletions
    int remainingExistingImages = _existingImageUrls.length;
    int newImages = _imageGallery.length;
    int totalImagesAfterUpdate = remainingExistingImages + newImages;

    if (totalImagesAfterUpdate == 0) {
      debugPrint(
          '❌ Submit validation failed - No images will remain after update');
      _showErrorSnackBar('At least one image is required');
      return;
    }

    // Tourism permit document is optional for edit mode
    // No validation needed for tourism permit document in edit mode

    setState(() => _isSubmitting = true);

    try {
      // Collect all form data
      final propertyData = {
        'title': _titleController.text.trim(),
        'content': _descriptionController.text.trim(),
        'price': double.parse(_priceController.text),
        'weekend_price': _weekendPriceController.text.isNotEmpty
            ? double.parse(_weekendPriceController.text)
            : null,
        'week_price': _weekPriceController.text.isNotEmpty
            ? double.parse(_weekPriceController.text)
            : null,
        'month_price': _monthPriceController.text.isNotEmpty
            ? double.parse(_monthPriceController.text)
            : null,
        'no_guests': int.parse(_guestsController.text),
        'beds': int.parse(_bedsController.text),
        'baths': int.parse(_bathsController.text),
        'service_category_id': _selectedCategory ?? 1,
        'property_type_id': _selectedPropertyType ?? 1,
        'short_term_policy_id': _selectedShortTermPolicy,
        'long_term_policy_id': _selectedLongTermPolicy,
        'facilities': _selectedFacilities,
        'requires_confirmation': _requiresConfirmation,
        'lat': _selectedLocation?.latitude ?? 24.7136,
        'lon': _selectedLocation?.longitude ?? 46.6753,
        'address': _locationAddress,
        'booking_rules': _bookingRulesController.text.trim(),
      };

      // Debug: Print the property data to see what we're sending
      debugPrint('🚀 Creating property with data: $propertyData');
      debugPrint(
          '📋 Selected facilities: $_selectedFacilities (count: ${_selectedFacilities.length}, type: ${_selectedFacilities.runtimeType})');
      debugPrint('🖼️ Images count: ${_imageGallery.length}');
      debugPrint('🎥 Video: ${_videoFile != null ? 'Yes' : 'No'}');

      // Use the cubit to update property - this will trigger the BlocConsumer
      // The _isSubmitting flag will be reset in the BlocConsumer listener
      debugPrint(
          '🔄 Starting property update for ID: ${widget.propertyData.id}');
      debugPrint('📝 Property data: $propertyData');

      context.read<PropertyCreationCubit>().updateProperty(
            propertyId: widget.propertyData.id,
            propertyData: propertyData,
            mainImage: _imageGallery.isNotEmpty ? _imageGallery.first : null,
            video: _videoFile,
            galleryImages: _imageGallery.length > 1 ? _imageGallery.sublist(1) : null,
            imagesToDelete: _imagesToDelete,
          );

      // Add a timeout to prevent infinite loading
      Timer(const Duration(seconds: 30), () {
        if (mounted && _isSubmitting) {
          debugPrint('⏰ Update operation timed out');
          setState(() => _isSubmitting = false);
          _showErrorSnackBar('Update operation timed out. Please try again.');
        }
      });
    } catch (e) {
      // Handle any synchronous errors (like parsing errors)
      debugPrint('❌ Error in _submitForm: $e');
      if (mounted) {
        setState(() => _isSubmitting = false);
        _showErrorSnackBar('An error occurred: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Performance monitoring
    final stopwatch = Stopwatch()..start();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      if (stopwatch.elapsedMilliseconds > 16) {
        // More than one frame (16ms)
        debugPrint(
            '⚠️ EditPropertyWizard build took ${stopwatch.elapsedMilliseconds}ms');
      }
    });

    return BlocProvider(
      create: (context) {
        try {
          final cubit = PropertyCreationCubit(getIt<PropertiesApiService>());
          // Load initial data immediately
          cubit.loadInitialData();
          return cubit;
        } catch (e) {
          debugPrint('Error creating PropertyCreationCubit: $e');
          final fallbackCubit =
              PropertyCreationCubit(getIt<PropertiesApiService>());
          fallbackCubit.loadInitialData();
          return fallbackCubit;
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        resizeToAvoidBottomInset: true,
        extendBody: false,
        body: SafeArea(
        bottom:
            false, // We'll handle bottom padding manually in navigation buttons
        child: BlocConsumer<PropertyCreationCubit, PropertyCreationState>(
          listener: (context, state) {
            if (state is PropertyCreationLoading) {
              // Ensure _isSubmitting is true when loading state is emitted
              // This handles edge cases where the state might change without going through _submitForm
              if (!_isSubmitting) {
                setState(() => _isSubmitting = true);
              }
            } else if (state is PropertyCreationSuccess) {
              debugPrint(
                  '✅ PropertyCreationSuccess: Property updated successfully');
              setState(() => _isSubmitting = false);



              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Property updated successfully'),
                  backgroundColor: Colors.green,
                ),
              );
              // Safe navigation back
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop(true);
              } else {
                context.go(RoutesKeys.kReelsViewTab);
              }
            } else if (state is PropertyCreationError) {
              debugPrint('❌ PropertyCreationError: ${state.message}');
              setState(() => _isSubmitting = false);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Update failed: ${state.message}'),
                  backgroundColor: theme.colorScheme.error,
                  duration: const Duration(seconds: 5),
                ),
              );
            } else if (state is PropertyCreationDataLoaded) {
              if (state.existingProperty != null) {
                // Populate form with existing property data
                _populateFormFromProperty(state.existingProperty!);
              }
              // Match facilities when data is loaded
              if (state.facilities.isNotEmpty) {
                _matchFacilitiesWithLoadedData();
              }
            }
          },
          builder: (context, state) {
            // Debug: Print current state
            debugPrint(
                'PropertyCreationWizard - Current state: ${state.runtimeType}');
            if (state is PropertyCreationDataLoaded) {
              debugPrint('Categories count: ${state.categories.length}');
              debugPrint('Property types count: ${state.propertyTypes.length}');
              debugPrint(
                  'Cancellation policies count: ${state.cancellationPolicies.length}');
              debugPrint('Facilities count: ${state.facilities.length}');
            }

            // Show loading only during form submission, not during initial data loading
            if (state is PropertyCreationLoading && _isSubmitting) {
              return Container(
                color: theme.scaffoldBackgroundColor,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        color: theme.primaryColor,
                        strokeWidth: 3,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        s.creatingProperty,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        s.pleaseWaitProcessing,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            }

            return Column(
              children: [
                // Progress indicator
                _buildProgressIndicator(),

                // Form content
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildBasicInfoStep(state),
                      _buildCategoryStep(state),
                      _buildDetailsStep(state),
                      _buildLocationStep(state),
                      _buildGalleryStep(state),
                      _buildReviewStep(),
                    ],
                  ),
                ),

                // Navigation buttons
                _buildNavigationButtons(state),
              ],
            );
          },
        ),
      ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final theme = Theme.of(context);
    final isRtl = Directionality.of(context) == TextDirection.rtl;
    final stepValidationStatus = _getStepValidationStatus();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          // Step indicators with icons
          Row(
            children: List.generate(_totalSteps, (index) {
              final stepConfig = _stepConfigs[index];
              final isActive = index == _currentStep;
              final isCompleted = index < _currentStep;
              final isValid = stepValidationStatus[index] ?? false;
              final isClickable =
                  index <= _currentStep || _canNavigateToStep(index);

              return Expanded(
                child: GestureDetector(
                  onTap: isClickable ? () => _navigateToStep(index) : null,
                  child: Container(
                    margin:
                        EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
                    child: Column(
                      children: [
                        // Step circle with icon
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isCompleted
                                ? Colors.green
                                : isActive
                                    ? (isValid
                                        ? theme.primaryColor
                                        : Colors.orange)
                                    : (isValid
                                        ? Colors.green.withValues(alpha: 0.3)
                                        : theme.dividerColor
                                            .withValues(alpha: 0.3)),
                            border: Border.all(
                              color: isCompleted
                                  ? Colors.green
                                  : isActive
                                      ? (isValid
                                          ? theme.primaryColor
                                          : Colors.orange)
                                      : (isValid
                                          ? Colors.green
                                          : theme.dividerColor),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            isCompleted
                                ? Icons.check
                                : isValid && !isActive
                                    ? Icons.check_circle_outline
                                    : stepConfig.icon,
                            color: isCompleted || isActive
                                ? Colors.white
                                : isValid
                                    ? Colors.green
                                    : theme.dividerColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Step title
                        Text(
                          isRtl ? stepConfig.titleAr : stepConfig.title,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isCompleted || isActive
                                ? theme.primaryColor
                                : theme.textTheme.bodySmall?.color
                                    ?.withOpacity(0.6),
                            fontWeight:
                                isActive ? FontWeight.w600 : FontWeight.normal,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 16),
          // Progress bar
          Row(
            children: List.generate(_totalSteps, (index) {
              final isActive = index <= _currentStep;
              final isCompleted = index < _currentStep;

              return Expanded(
                child: Container(
                  height: 4,
                  margin:
                      EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
                  decoration: BoxDecoration(
                    color: isCompleted
                        ? Colors.green
                        : isActive
                            ? theme.primaryColor
                            : theme.dividerColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
          20, 20, 20, 100), // Extra bottom padding for navigation buttons
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.info_outline,
                      color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.basicInformation,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.basicInformationDesc,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Enhanced form fields
          _buildEnhancedTextField(
            controller: _titleController,
            label: s.propertyTitle,
            hint: s.propertyTitleHint,
            icon: Icons.title,
            maxLength: 100,
          ),
          const SizedBox(height: 20),

          _buildEnhancedTextField(
            controller: _descriptionController,
            label: s.propertyDescription,
            hint: s.propertyDescriptionHint,
            icon: Icons.description,
            maxLines: 4,
            maxLength: 500,
          ),
          const SizedBox(height: 20),

          _buildEnhancedTextField(
            controller: _priceController,
            label: s.pricePerNight,
            hint: s.priceHint,
            icon: Icons.attach_money,
            keyboardType: TextInputType.number,
            prefixText: 'SAR ',
          ),
          const SizedBox(height: 16),

          // Optional pricing fields
          Text(
            'Optional Pricing (Leave empty if not applicable)',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.7),
                ),
          ),
          const SizedBox(height: 12),

          _buildEnhancedTextField(
            controller: _weekendPriceController,
            label: 'Weekend Price (Friday & Saturday)',
            hint: 'Enter weekend price per night',
            icon: Icons.weekend,
            keyboardType: TextInputType.number,
            prefixText: 'SAR ',
          ),
          const SizedBox(height: 16),

          _buildEnhancedTextField(
            controller: _weekPriceController,
            label: 'Weekly Price (7+ nights)',
            hint: 'Enter price per week',
            icon: Icons.date_range,
            keyboardType: TextInputType.number,
            prefixText: 'SAR ',
          ),
          const SizedBox(height: 16),

          _buildEnhancedTextField(
            controller: _monthPriceController,
            label: 'Monthly Price (30+ nights)',
            hint: 'Enter price per month',
            icon: Icons.calendar_month,
            keyboardType: TextInputType.number,
            prefixText: 'SAR ',
          ),

          const SizedBox(height: 16),

          // Price guidance card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border:
                  Border.all(color: theme.primaryColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.lightbulb_outline,
                    color: theme.primaryColor, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    s.priceGuidance,
                    style: theme.textTheme.bodySmall
                        ?.copyWith(color: theme.primaryColor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
          20, 20, 20, 100), // Extra bottom padding for navigation buttons
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.category,
                      color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.categoryAndType,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.categoryTypeDescription,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Enhanced selections
          _buildEnhancedCategorySelection(state),
          const SizedBox(height: 20),

          _buildEnhancedPropertyTypeSelection(state),
          const SizedBox(height: 20),

          _buildEnhancedCancellationPolicySelection(state),
        ],
      ),
    );
  }



  Widget _buildDetailsStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
          20, 20, 20, 100), // Extra bottom padding for navigation buttons
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced header with theme colors
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(Icons.home_work,
                      color: theme.colorScheme.onPrimary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.propertyDetails,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        s.propertyDetailsDescription,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Use Column layout for better width handling
          Column(
            children: [
              // First row: Max Guests (full width for better label visibility)
              _buildEnhancedTextField(
                controller: _guestsController,
                label: s.maxGuests,
                hint: 'Enter maximum number of guests',
                icon: Icons.people,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),

              // Second row: Bedrooms and Bathrooms (side by side)
              Row(
                children: [
                  Expanded(
                    child: _buildEnhancedTextField(
                      controller: _bedsController,
                      label: s.bedrooms,
                      hint: 'Beds',
                      icon: Icons.bed,
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildEnhancedTextField(
                      controller: _bathsController,
                      label: s.bathrooms,
                      hint: 'Baths',
                      icon: Icons.bathtub,
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Facilities selection
          _buildFacilitiesSelection(state),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
          16, 16, 16, 100), // Extra bottom padding for navigation buttons
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.reviewAndSubmit,
            style: theme.textTheme.titleLarge
                ?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          _buildReviewCard(s.title, _titleController.text),
          _buildReviewCard(s.description, _descriptionController.text),
          _buildReviewCard(
              s.pricePerNight, '${_priceController.text} SAR/night'),
          _buildReviewCard(s.guests, _guestsController.text),
          _buildReviewCard(s.bedrooms, _bedsController.text),
          _buildReviewCard(s.bathrooms, _bathsController.text),
          _buildReviewCard(s.category, _getSelectedCategoryName()),
          _buildReviewCard(s.propertyType, _getSelectedPropertyTypeName()),
          _buildReviewCard(
              s.cancellationPolicy, _getSelectedCancellationPolicyName()),
          _buildReviewCard(s.facilities, _getSelectedFacilitiesNames()),
          _buildReviewCard('Reservation Confirmation',
              _requiresConfirmation ? 'Required' : 'Not Required'),
          _buildReviewCard(
              s.bookingRules,
              _bookingRulesController.text.trim().isEmpty
                  ? s.notProvided
                  : _bookingRulesController.text.trim()),

          _buildReviewCard(s.location, _locationAddress ?? s.notSelected),
          _buildReviewCard(s.photos, '${_imageGallery.length} ${s.images}'),
          _buildReviewCard(s.video, _videoFile != null ? s.added : s.notAdded),
        ],
      ),
    );
  }

  Widget _buildLocationStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
          16, 16, 16, 100), // Extra bottom padding for navigation buttons
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.locationAndAddress,
            style: AppTextStyles.font20Bold,
          ),
          const SizedBox(height: 24),

          // Location picker
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.location_on, color: theme.primaryColor),
                      const SizedBox(width: 8),
                      Text(s.propertyLocation),
                      const Spacer(),
                      if (_selectedLocation != null)
                        const Icon(Icons.check_circle, color: Colors.green),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_selectedLocation != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle,
                              color: Colors.green, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(s.locationSelected,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.bold)),
                                if (_locationAddress != null)
                                  Text(_locationAddress!,
                                      style: theme.textTheme.bodySmall),
                                Text(
                                  'Lat: ${_selectedLocation!.latitude.toStringAsFixed(6)}, '
                                  'Lng: ${_selectedLocation!.longitude.toStringAsFixed(6)}',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface
                                        .withValues(alpha: 0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  ElevatedButton.icon(
                    onPressed: _openLocationPicker,
                    icon: Icon(_selectedLocation != null
                        ? Icons.edit_location
                        : Icons.add_location),
                    label: Text(_selectedLocation != null
                        ? s.changeLocation
                        : s.selectLocation),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.primaryColor,
                      foregroundColor: theme.colorScheme.onPrimary,
                      minimumSize: const Size(double.infinity, 48),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGalleryStep(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(
          16, 16, 16, 100), // Extra bottom padding for navigation buttons
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.photosAndVideo,
            style: theme.textTheme.titleLarge
                ?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Image gallery section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.photo_library, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyPhotos),
                      const Spacer(),
                      Text('${_getTotalImageCount()}/10',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.6),
                          )),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Image grid - show both existing and new images
                  if (_getTotalImageCount() > 0) ...[
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: _getTotalImageCount() + 1,
                      cacheExtent: 200, // Cache images for better performance
                      itemBuilder: (context, index) {
                        if (index == _getTotalImageCount()) {
                          return _buildAddImageButton();
                        }
                        return _buildImageItemAtIndex(index);
                      },
                    ),
                  ] else ...[
                    _buildEmptyGalleryState(),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Video section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.videocam, color: Color(0xFFFEC53A)),
                      const SizedBox(width: 8),
                      Text(s.propertyVideoOptional),
                      const Spacer(),
                      if (_videoFile != null)
                        const Icon(Icons.check_circle, color: Colors.green),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (_videoFile != null) ...[
                    _buildVideoPreview(),
                    const SizedBox(height: 16),
                  ],
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _pickVideo,
                          icon: const Icon(Icons.video_library),
                          label: Text(_videoFile != null
                              ? 'Change Video'
                              : 'Add Video'),
                        ),
                      ),
                      if (_videoFile != null) ...[
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _removeVideo,
                          icon: Icon(Icons.delete,
                              color: theme.colorScheme.error),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(String label, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 100,
              child: Text(
                label,
                style: AppTextStyles.font14SemiBold,
              ),
            ),
            Expanded(
              child: Text(
                value.isEmpty ? 'Not provided' : value,
                style: AppTextStyles.font14Regular,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationButtons(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: 20 +
            MediaQuery.of(context)
                .padding
                .bottom, // Add bottom safe area padding
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  debugPrint(
                      '🔙 Back button pressed - Current step: $_currentStep');
                  _previousStep();
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(
                    color: theme.brightness == Brightness.dark
                        ? const Color(0xFFFFD234) // Yellow in dark mode
                        : theme.primaryColor, // Yellow in light mode
                    width: 2,
                  ),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                  backgroundColor: Colors.transparent,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.arrow_back,
                      color: theme.brightness == Brightness.dark
                          ? const Color(0xFFFFD234) // Yellow in dark mode
                          : theme.primaryColor, // Yellow in light mode
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      s.previous,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.brightness == Brightness.dark
                            ? const Color(0xFFFFD234) // Yellow in dark mode
                            : theme.primaryColor, // Yellow in light mode
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isSubmitting
                  ? null
                  : _currentStep == _totalSteps - 1
                      ? (_validateAllSteps() ? _submitForm : null)
                      : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    _currentStep == _totalSteps - 1 && !_validateAllSteps()
                        ? theme.disabledColor
                        : theme.primaryColor,
                foregroundColor:
                    _currentStep == _totalSteps - 1 && !_validateAllSteps()
                        ? theme.colorScheme.onSurface.withValues(alpha: 0.6)
                        : theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                elevation:
                    _currentStep == _totalSteps - 1 && !_validateAllSteps()
                        ? 0
                        : 2,
              ),
              child: _isSubmitting
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.onPrimary),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _currentStep == _totalSteps - 1
                              ? (_validateAllSteps()
                                  ? s.editProperty
                                  : 'Complete all required fields')
                              : s.next,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: _currentStep == _totalSteps - 1 &&
                                    !_validateAllSteps()
                                ? theme.colorScheme.onSurface
                                    .withValues(alpha: 0.6)
                                : theme.colorScheme.onPrimary,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          _currentStep == _totalSteps - 1
                              ? (_validateAllSteps()
                                  ? Icons.check
                                  : Icons.warning_outlined)
                              : Icons.arrow_forward,
                          color: _currentStep == _totalSteps - 1 &&
                                  !_validateAllSteps()
                              ? theme.colorScheme.onSurface
                                  .withValues(alpha: 0.6)
                              : theme.colorScheme.onPrimary,
                          size: 18,
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced selection methods
  Widget _buildEnhancedCategorySelection(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get categories from API data or use empty list if not loaded
    final categories = state is PropertyCreationDataLoaded
        ? state.categories
        : <ServiceCategory>[];

    debugPrint(
        '_buildEnhancedCategorySelection - Categories count: ${categories.length}');

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child:
                      Icon(Icons.category, color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.selectCategory,
                  style: theme.textTheme.titleMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Category grid
            if (categories.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        Icon(Icons.error_outline,
                            color: theme.colorScheme.error, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load categories',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context
                                .read<PropertyCreationCubit>()
                                .loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading categories...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 2.5,
                ),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  final category = categories[index];
                  final isSelected = _selectedCategory == category.id;

                  return GestureDetector(
                    onTap: () {
                      setState(() => _selectedCategory = category.id);
                      HapticFeedback.selectionClick();
                      // Load facilities for the selected category
                      context
                          .read<PropertyCreationCubit>()
                          .loadFacilitiesByCategory(category.id);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234).withValues(
                                    alpha:
                                        0.15) // Yellow background in dark mode
                                : theme.primaryColor.withValues(alpha: 0.1))
                            : theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? (theme.brightness == Brightness.dark
                                  ? const Color(
                                      0xFFFFD234) // Yellow border in dark mode
                                  : theme.primaryColor)
                              : theme.dividerColor,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.category,
                            color: isSelected
                                ? (theme.brightness == Brightness.dark
                                    ? const Color(
                                        0xFFFFD234) // Yellow in dark mode
                                    : theme.primaryColor)
                                : theme.colorScheme.onSurface
                                    .withValues(alpha: 0.6),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              category.title,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: isSelected
                                    ? (theme.brightness == Brightness.dark
                                        ? const Color(
                                            0xFFFFD234) // Yellow in dark mode
                                        : theme.primaryColor)
                                    : theme.colorScheme.onSurface
                                        .withValues(alpha: 0.8),
                              ),
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedPropertyTypeSelection(PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get property types from API data or use empty list if not loaded
    final propertyTypes = state is PropertyCreationDataLoaded
        ? state.propertyTypes
        : <PropertyTypeModel>[];

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.home_work,
                      color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.propertyType,
                  style: theme.textTheme.titleMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Property type list
            if (propertyTypes.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        Icon(Icons.error_outline,
                            color: theme.colorScheme.error, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load property types',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context
                                .read<PropertyCreationCubit>()
                                .loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading property types...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else
              ...propertyTypes.map((type) {
                final isSelected = _selectedPropertyType == type.id;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: GestureDetector(
                    onTap: () {
                      setState(() => _selectedPropertyType = type.id);
                      HapticFeedback.selectionClick();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234).withValues(
                                    alpha:
                                        0.15) // Yellow background in dark mode
                                : theme.primaryColor.withValues(alpha: 0.1))
                            : theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? (theme.brightness == Brightness.dark
                                  ? const Color(
                                      0xFFFFD234) // Yellow border in dark mode
                                  : theme.primaryColor)
                              : theme.dividerColor,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? (theme.brightness == Brightness.dark
                                      ? const Color(
                                          0xFFFFD234) // Yellow in dark mode
                                      : theme.primaryColor)
                                  : theme.colorScheme.outline,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.home,
                              color: isSelected
                                  ? theme.colorScheme.onPrimary
                                  : theme.colorScheme.onSurface
                                      .withValues(alpha: 0.6),
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  type.title,
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: isSelected
                                        ? (theme.brightness == Brightness.dark
                                            ? const Color(
                                                0xFFFFD234) // Yellow in dark mode
                                            : theme.primaryColor)
                                        : theme.colorScheme.onSurface
                                            .withValues(alpha: 0.8),
                                  ),
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  'Property type option',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurface
                                        .withValues(alpha: 0.6),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (isSelected)
                            Icon(Icons.check_circle,
                                color: theme.brightness == Brightness.dark
                                    ? const Color(
                                        0xFFFFD234) // Yellow in dark mode
                                    : theme.primaryColor,
                                size: 20),
                        ],
                      ),
                    ),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedCancellationPolicySelection(
      PropertyCreationState state) {
    final s = S.of(context);
    final theme = Theme.of(context);

    // Get cancellation policies from API data or use empty list if not loaded
    final policies = state is PropertyCreationDataLoaded
        ? state.cancellationPolicies
        : <CancellationPolicyModel>[];

    // Separate policies by duration type
    final shortTermPolicies = policies
        .where((p) => p.durationType == 'short' || p.durationType == 'both')
        .toList();
    final longTermPolicies = policies
        .where((p) => p.durationType == 'long' || p.durationType == 'both')
        .toList();

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child:
                      Icon(Icons.policy, color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  s.cancellationPolicy,
                  style: theme.textTheme.titleMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Policy list
            if (policies.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        Icon(Icons.error_outline,
                            color: theme.colorScheme.error, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load cancellation policies',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context
                                .read<PropertyCreationCubit>()
                                .loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading cancellation policies...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else ...[
              // Short-term policies section
              if (shortTermPolicies.isNotEmpty) ...[
                _buildPolicySectionHeader(
                  'Short-term Policies (≤28 days)',
                  'سياسات قصيرة المدى (≤28 يوم)',
                  Icons.schedule,
                  theme,
                ),
                const SizedBox(height: 12),
                ...shortTermPolicies
                    .map((policy) => _buildPolicyCard(policy, theme)),
                const SizedBox(height: 20),
              ],

              // Long-term policies section
              if (longTermPolicies.isNotEmpty) ...[
                _buildPolicySectionHeader(
                  'Long-term Policies (>28 days)',
                  'سياسات طويلة المدى (>28 يوم)',
                  Icons.event,
                  theme,
                ),
                const SizedBox(height: 12),
                ...longTermPolicies
                    .map((policy) => _buildPolicyCard(policy, theme)),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPolicySectionHeader(
      String titleEn, String titleAr, IconData icon, ThemeData theme) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(icon, color: theme.primaryColor, size: 18),
          const SizedBox(width: 8),
          Text(
            isArabic ? titleAr : titleEn,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyCard(CancellationPolicyModel policy, ThemeData theme) {
    final isShortTerm =
        policy.durationType == 'short' || policy.durationType == 'both';
    final isLongTerm =
        policy.durationType == 'long' || policy.durationType == 'both';

    bool isSelected = false;
    if (isShortTerm && _selectedShortTermPolicy == policy.id) isSelected = true;
    if (isLongTerm && _selectedLongTermPolicy == policy.id) isSelected = true;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: () {
          setState(() {
            if (isShortTerm && isLongTerm) {
              // Policy applies to both - user can choose which slot to use
              if (_selectedShortTermPolicy == null) {
                _selectedShortTermPolicy = policy.id;
              } else if (_selectedLongTermPolicy == null) {
                _selectedLongTermPolicy = policy.id;
              } else {
                // Both slots filled, replace short term
                _selectedShortTermPolicy = policy.id;
              }
            } else if (isShortTerm) {
              _selectedShortTermPolicy = policy.id;
            } else if (isLongTerm) {
              _selectedLongTermPolicy = policy.id;
            }
          });
          HapticFeedback.selectionClick();
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? (theme.brightness == Brightness.dark
                    ? const Color(0xFFFFD234).withValues(
                        alpha: 0.15) // Yellow background in dark mode
                    : theme.primaryColor.withValues(alpha: 0.1))
                : theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? (theme.brightness == Brightness.dark
                      ? const Color(0xFFFFD234) // Yellow border in dark mode
                      : theme.primaryColor)
                  : theme.dividerColor,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (theme.brightness == Brightness.dark
                          ? const Color(0xFFFFD234) // Yellow in dark mode
                          : theme.primaryColor)
                      : theme.colorScheme.outline,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.schedule,
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      policy.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected
                            ? (theme.brightness == Brightness.dark
                                ? const Color(0xFFFFD234) // Yellow in dark mode
                                : theme.primaryColor)
                            : theme.colorScheme.onSurface
                                .withValues(alpha: 0.8),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      policy.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle,
                    color: theme.brightness == Brightness.dark
                        ? const Color(0xFFFFD234) // Yellow in dark mode
                        : theme.primaryColor,
                    size: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFacilitiesSelection(PropertyCreationState state) {
    final theme = Theme.of(context);

    // Get facilities from API data or use empty list if not loaded
    final facilities = state is PropertyCreationDataLoaded
        ? state.facilities
        : <FacilityModel>[];

    final groupedFacilities =
        state is PropertyCreationDataLoaded ? state.groupedFacilities : null;

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(Icons.home_repair_service,
                      color: theme.primaryColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Select Facilities',
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                  ),
                ),
                // Removed refresh button to prevent rate limiting issues
              ],
            ),
            const SizedBox(height: 16),

            if (facilities.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(40),
                  child: Column(
                    children: [
                      if (state is PropertyCreationError) ...[
                        Icon(Icons.error_outline,
                            color: theme.colorScheme.error, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load facilities',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            context
                                .read<PropertyCreationCubit>()
                                .loadInitialData();
                          },
                          child: const Text('Retry'),
                        ),
                      ] else ...[
                        CircularProgressIndicator(color: theme.primaryColor),
                        const SizedBox(height: 16),
                        Text(
                          'Loading facilities...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )
            else if (groupedFacilities != null && groupedFacilities.isNotEmpty)
              // Display grouped facilities
              ...groupedFacilities.entries.map((entry) {
                final categoryName = entry.key;
                final categoryFacilities = entry.value;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: theme.primaryColor.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: theme.primaryColor.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.category,
                              color: theme.primaryColor, size: 18),
                          const SizedBox(width: 8),
                          Text(
                            categoryName,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: categoryFacilities.map((facility) {
                        final isSelected =
                            _selectedFacilities.contains(facility.id);
                        return FilterChip(
                          label: Text(facility.title),
                          selected: isSelected,
                          selectedColor: theme.brightness == Brightness.dark
                              ? const Color(0xFFFFD234).withValues(
                                  alpha: 0.2) // Yellow background in dark mode
                              : theme.primaryColor.withValues(
                                  alpha:
                                      0.2), // Yellow background in light mode
                          checkmarkColor: theme.brightness == Brightness.dark
                              ? const Color(
                                  0xFFFFD234) // Yellow checkmark in dark mode
                              : theme
                                  .primaryColor, // Yellow checkmark in light mode
                          onSelected: (selected) {
                            setState(() {
                              if (selected) {
                                _selectedFacilities.add(facility.id);
                              } else {
                                _selectedFacilities.remove(facility.id);
                              }
                            });
                            HapticFeedback.selectionClick();
                          },
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                  ],
                );
              })
            else
              // Display flat list if no grouping available
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: facilities.map((facility) {
                  final isSelected = _selectedFacilities.contains(facility.id);
                  return FilterChip(
                    label: Text(facility.title),
                    selected: isSelected,
                    selectedColor: theme.brightness == Brightness.dark
                        ? const Color(0xFFFFD234).withValues(
                            alpha: 0.2) // Yellow background in dark mode
                        : theme.primaryColor.withValues(
                            alpha: 0.2), // Yellow background in light mode
                    checkmarkColor: theme.brightness == Brightness.dark
                        ? const Color(
                            0xFFFFD234) // Yellow checkmark in dark mode
                        : theme.primaryColor, // Yellow checkmark in light mode
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedFacilities.add(facility.id);
                        } else {
                          _selectedFacilities.remove(facility.id);
                        }
                      });
                      HapticFeedback.selectionClick();
                    },
                  );
                }).toList(),
              ),

            // Reservation confirmation toggle
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.dividerColor,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.check_circle_outline,
                      color: theme.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Require Reservation Confirmation',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Guests must wait for your approval before their reservation is confirmed',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface
                                .withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: _requiresConfirmation,
                    onChanged: (value) {
                      setState(() {
                        _requiresConfirmation = value;
                      });
                      HapticFeedback.selectionClick();
                    },
                    activeColor: theme.brightness == Brightness.dark
                        ? const Color(0xFFFFD234) // Yellow in dark mode
                        : theme.primaryColor, // Yellow in light mode
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSelectedFacilitiesNames() {
    if (_selectedFacilities.isEmpty) return 'None selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final facilities = currentState.facilities;
      final selectedNames = <String>[];

      for (final id in _selectedFacilities) {
        final facility = facilities.firstWhere((f) => f.id == id,
            orElse: () => const FacilityModel(id: 0, title: '', order: 0));
        if (facility.title.isNotEmpty) {
          selectedNames.add(facility.title);
        }
      }

      if (selectedNames.isNotEmpty) {
        return selectedNames.join(', ');
      }
    }

    return '${_selectedFacilities.length} selected';
  }

  String _getSelectedCategoryName() {
    if (_selectedCategory == null) return 'Not selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final category = currentState.categories.firstWhere(
        (c) => c.id == _selectedCategory,
        orElse: () => const ServiceCategory(
            id: 0, title: 'Unknown', icon: '', image: '', order: 0),
      );
      return category.title;
    }

    return 'Selected';
  }

  String _getSelectedPropertyTypeName() {
    if (_selectedPropertyType == null) return 'Not selected';

    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final propertyType = currentState.propertyTypes.firstWhere(
        (pt) => pt.id == _selectedPropertyType,
        orElse: () => const PropertyTypeModel(
            id: 0, title: 'Unknown', order: 0, createdAt: ''),
      );
      return propertyType.title;
    }

    return 'Selected';
  }

  String _getSelectedCancellationPolicyName() {
    final currentState = context.read<PropertyCreationCubit>().state;
    if (currentState is PropertyCreationDataLoaded) {
      final policies = currentState.cancellationPolicies;
      List<String> selectedNames = [];

      if (_selectedShortTermPolicy != null) {
        final shortPolicy = policies.firstWhere(
          (cp) => cp.id == _selectedShortTermPolicy,
          orElse: () => const CancellationPolicyModel(
            id: 0,
            nameEn: 'Unknown',
            nameAr: '',
            name: 'Unknown',
            descriptionEn: '',
            descriptionAr: '',
            description: '',
            policyType: '',
            durationType: '',
            cancellationWindowHours: 0,
            refundPercentage: 0.0,
            serviceFeeRefundable: false,
            cleaningFeeRefundable: false,
            isActive: true,
            order: 0,
            createdAt: '',
            updatedAt: '',
          ),
        );
        selectedNames.add('Short: ${shortPolicy.name}');
      }

      if (_selectedLongTermPolicy != null) {
        final longPolicy = policies.firstWhere(
          (cp) => cp.id == _selectedLongTermPolicy,
          orElse: () => const CancellationPolicyModel(
            id: 0,
            nameEn: 'Unknown',
            nameAr: '',
            name: 'Unknown',
            descriptionEn: '',
            descriptionAr: '',
            description: '',
            policyType: '',
            durationType: '',
            cancellationWindowHours: 0,
            refundPercentage: 0.0,
            serviceFeeRefundable: false,
            cleaningFeeRefundable: false,
            isActive: true,
            order: 0,
            createdAt: '',
            updatedAt: '',
          ),
        );
        selectedNames.add('Long: ${longPolicy.name}');
      }

      return selectedNames.isEmpty ? 'Not selected' : selectedNames.join(', ');
    }

    return _selectedShortTermPolicy != null || _selectedLongTermPolicy != null
        ? 'Selected'
        : 'Not selected';
  }

  // Location picker methods
  void _openLocationPicker() async {
    try {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => LocationMapPicker(
            initialLatitude: _selectedLocation?.latitude,
            initialLongitude: _selectedLocation?.longitude,
            onLocationSelected: (latitude, longitude) {
              setState(() {
                _selectedLocation = LatLng(latitude, longitude);
                _locationAddress = null; // Will be loaded by reverse geocoding
              });

              // Load address for the selected location
              _loadAddressForLocation(latitude, longitude);



              // Show success message
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(S.of(context).locationSelected),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open location picker: $e')),
        );
      }
    }
  }

  // Load address for selected location
  Future<void> _loadAddressForLocation(
      double latitude, double longitude) async {
    try {
      final address =
          await GeocodingService.getAddressFromCoordinates(latitude, longitude);
      if (mounted) {
        setState(() {
          _locationAddress = address ?? 'Location selected';
        });
      }
    } catch (e) {
      // Silently fail - address is not critical
      if (mounted) {
        setState(() {
          _locationAddress = 'Location selected';
        });
      }
    }
  }

  // Gallery helper methods
  int _getTotalImageCount() {
    return _existingImageUrls.length + _imageGallery.length;
  }

  Widget _buildImageItemAtIndex(int index) {
    // First show existing images, then new images
    if (index < _existingImageUrls.length) {
      return _buildExistingImageItem(index);
    } else {
      return _buildNewImageItem(index - _existingImageUrls.length);
    }
  }

  Widget _buildExistingImageItem(int index) {
    final imageUrl = _existingImageUrls[index];
    final theme = Theme.of(context);

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: theme.colorScheme.surface,
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      color: theme.primaryColor,
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: theme.colorScheme.surface,
                  child: Icon(Icons.error, color: theme.colorScheme.error),
                );
              },
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeExistingImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
        // Badge to indicate this is an existing image
        Positioned(
          bottom: 4,
          left: 4,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Existing',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNewImageItem(int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              _imageGallery[index],
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              cacheWidth: 200, // Optimize memory usage
              cacheHeight: 200,
              errorBuilder: (context, error, stackTrace) {
                final theme = Theme.of(context);
                return Container(
                  color: theme.colorScheme.surface,
                  child: Icon(Icons.error, color: theme.colorScheme.error),
                );
              },
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeNewImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
        // Badge to indicate this is a new image
        Positioned(
          bottom: 4,
          left: 4,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'New',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _removeExistingImage(int index) {
    setState(() {
      final imageUrl = _existingImageUrls[index];
      _existingImageUrls.removeAt(index);
      _imagesToDelete.add(imageUrl);
    });
    HapticFeedback.lightImpact();
  }

  void _removeNewImage(int index) {
    setState(() {
      _imageGallery.removeAt(index);
    });
    HapticFeedback.lightImpact();
  }

  // Gallery methods
  Widget _buildAddImageButton() {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border:
              Border.all(color: theme.dividerColor, style: BorderStyle.solid),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 32),
            const SizedBox(height: 4),
            Text('Add Photo',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                )),
          ],
        ),
      ),
    );
  }



  Widget _buildEmptyGalleryState() {
    final s = S.of(context);
    final theme = Theme.of(context);
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.dividerColor, style: BorderStyle.solid),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.photo_library_outlined,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              size: 48),
          const SizedBox(height: 8),
          Text(s.noPhotosAdded,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              )),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _showImagePickerOptions,
            icon: const Icon(Icons.add_photo_alternate),
            label: Text(s.addPhotos),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.primaryColor,
              foregroundColor: theme.colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    final s = S.of(context);
    showModalBottomSheet(
      context: context,
      useSafeArea: true,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text(s.takePhoto),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text(s.chooseFromGallery),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: Text(s.chooseMultiple),
              onTap: () {
                Navigator.pop(context);
                _pickMultipleImages();
              },
            ),

            // Bottom safe area padding to avoid bottom navigation bar
            // Add extra padding for bottom navigation bar (typically 80-90px)
            SizedBox(height: MediaQuery.of(context).padding.bottom + 80),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? pickedFile =
          await _picker.pickImage(source: ImageSource.camera);
      if (pickedFile != null) {
        setState(() {
          _imageGallery.add(File(pickedFile.path));
        });
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to take photo: $e')),
        );
      }
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? pickedFile =
          await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          _imageGallery.add(File(pickedFile.path));
        });
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick image: $e')),
        );
      }
    }
  }

  Future<void> _pickMultipleImages() async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage();
      if (pickedFiles.isNotEmpty) {
        setState(() {
          for (final file in pickedFiles) {
            if (_imageGallery.length < 10) {
              _imageGallery.add(File(file.path));
            }
          }
        });
        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick images: $e')),
        );
      }
    }
  }



  // Video methods
  Widget _buildVideoPreview() {
    if (_videoController != null && _videoController!.value.isInitialized) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.black,
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
            ),
            Positioned(
              bottom: 8,
              right: 8,
              child: FloatingActionButton.small(
                onPressed: () {
                  setState(() {
                    _videoController!.value.isPlaying
                        ? _videoController!.pause()
                        : _videoController!.play();
                  });
                },
                child: Icon(
                  _videoController!.value.isPlaying
                      ? Icons.pause
                      : Icons.play_arrow,
                ),
              ),
            ),
          ],
        ),
      );
    }

    final theme = Theme.of(context);
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library,
                size: 48,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6)),
            const SizedBox(height: 8),
            Text('Video Preview',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                )),
          ],
        ),
      ),
    );
  }

  Future<void> _pickVideo() async {
    try {
      final XFile? pickedFile =
          await _picker.pickVideo(source: ImageSource.gallery);
      if (pickedFile != null) {
        setState(() {
          _videoFile = File(pickedFile.path);
        });

        // Initialize video controller
        _videoController?.dispose();
        _videoController = VideoPlayerController.file(_videoFile!)
          ..initialize().then((_) {
            setState(() {});
          });

        HapticFeedback.selectionClick();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to pick video: $e')),
        );
      }
    }
  }

  void _removeVideo() {
    setState(() {
      _videoFile = null;
      _videoController?.dispose();
      _videoController = null;
    });
    HapticFeedback.lightImpact();
  }

  // Enhanced UI Components
  Widget _buildEnhancedTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    int? maxLength,
    TextInputType? keyboardType,
    String? prefixText,
  }) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        maxLength: maxLength,
        keyboardType: keyboardType,
        style: theme.textTheme.bodyLarge,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixText: prefixText,
          labelStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: theme.primaryColor, size: 20),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: theme.dividerColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: theme.dividerColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: theme.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: theme.colorScheme.surface,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }
}
