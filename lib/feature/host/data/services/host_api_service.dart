import 'package:flutter/foundation.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/host/data/models/host_dashboard_model.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';

class HostApiService {
  final DioConsumer _dioConsumer;

  HostApiService(this._dioConsumer);

  /// Get host dashboard data
  Future<HostDashboardModel> getHostDashboard() async {
    try {
      final response = await _dioConsumer.get('/api/host/dashboard');

      if (response['success'] == true) {
        return HostDashboardModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch dashboard data');
      }
    } catch (e) {
      // Return dummy data for now until backend endpoint is implemented
      return _getDummyDashboardData();
    }
  }

  /// Get host reservations
  Future<List<ReservationModel>> getHostReservations({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (status != null) 'status': status,
        'host_view': true, // To get reservations for host's properties
      };

      final response = await _dioConsumer.get(
        EndPoints.reservationsList,
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => ReservationModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch reservations');
      }
    } catch (e) {
      // Return dummy data for now
      return _getDummyReservations();
    }
  }

  /// Get host properties/listings
  Future<List<PropertyItemModel>> getHostProperties({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        'host_view': true, // To get only current user's properties
      };

      final response = await _dioConsumer.get(
        EndPoints.itemsList,
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        debugPrint('🏠 Fetching ${data.length} properties from API');

        final properties = <PropertyItemModel>[];
        for (int i = 0; i < data.length; i++) {
          try {
            final property = PropertyItemModel.fromJson(data[i]);
            properties.add(property);
          } catch (e) {
            debugPrint('❌ Error parsing property at index $i: $e');
            debugPrint('📋 Property data: ${data[i]}');
            // Continue with other properties instead of failing completely
            continue;
          }
        }

        debugPrint('✅ Successfully parsed ${properties.length} properties');
        return properties;
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch properties');
      }
    } catch (e) {
      throw Exception('Failed to fetch properties: ${e.toString()}');
    }
  }

  /// Create new property listing
  Future<PropertyItemModel> createProperty(Map<String, dynamic> propertyData) async {
    try {
      final response = await _dioConsumer.post(
        EndPoints.createProperty,
        data: propertyData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return PropertyItemModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create property');
      }
    } catch (e) {
      throw Exception('Failed to create property: ${e.toString()}');
    }
  }

  /// Update property listing
  Future<PropertyItemModel> updateProperty(int propertyId, Map<String, dynamic> propertyData) async {
    try {
      final response = await _dioConsumer.put(
        '${EndPoints.itemsUpdate}/$propertyId',
        data: propertyData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return PropertyItemModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update property');
      }
    } catch (e) {
      throw Exception('Failed to update property: ${e.toString()}');
    }
  }

  /// Upload property gallery images
  Future<bool> uploadPropertyGallery(int propertyId, List<String> imagePaths) async {
    try {
      final formData = <String, dynamic>{};
      
      for (int i = 0; i < imagePaths.length; i++) {
        formData['images[$i]'] = await _createMultipartFile(imagePaths[i]);
      }

      final response = await _dioConsumer.post(
        '${EndPoints.itemsUploadGallery}/$propertyId',
        data: formData,
        isFormData: true,
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to upload gallery: ${e.toString()}');
    }
  }

  /// Helper method to create multipart file
  Future<dynamic> _createMultipartFile(String filePath) async {
    // This would be implemented based on the file upload requirements
    // For now, return the file path
    return filePath;
  }

  /// Get host financial summary
  Future<HostFinancialData> getFinancialSummary() async {
    try {
      final response = await _dioConsumer.get('/api/host/financial-summary');
      
      if (response['success'] == true) {
        return HostFinancialData.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch financial data');
      }
    } catch (e) {
      // Return dummy data for now
      return const HostFinancialData(
        walletBalance: 2450.75,
        totalEarnings: 15680.50,
        pendingEarnings: 890.25,
        totalWithdrawn: 12340.00,
        thisMonthEarnings: 3650.75,
      );
    }
  }

  /// Dummy data methods (to be removed when backend is ready)
  HostDashboardModel _getDummyDashboardData() {
    return HostDashboardModel(
      financialData: const HostFinancialData(
        walletBalance: 2450.75,
        totalEarnings: 15680.50,
        pendingEarnings: 890.25,
        totalWithdrawn: 12340.00,
        thisMonthEarnings: 3650.75,
      ),
      recentReservations: _getDummyReservationSummaries(),
      recentReviews: _getDummyReviewSummaries(),
      statistics: const HostStatistics(
        totalProperties: 5,
        totalReservations: 48,
        totalReviews: 42,
        averageRating: 4.7,
        totalViews: 1250,
      ),
      earningsChart: _getDummyEarningsChart(),
      bookingsChart: _getDummyBookingsChart(),
    );
  }

  List<ReservationModel> _getDummyReservations() {
    // Return dummy reservation data
    return [];
  }

  List<ReservationSummary> _getDummyReservationSummaries() {
    return [
      const ReservationSummary(
        id: 1,
        guestName: 'أحمد محمد',
        propertyName: 'شقة فاخرة في الرياض',
        checkIn: '2024-01-15',
        checkOut: '2024-01-18',
        nights: 3,
        amount: 450.0,
        status: 'confirmed',
      ),
      const ReservationSummary(
        id: 2,
        guestName: 'سارة أحمد',
        propertyName: 'فيلا مع مسبح',
        checkIn: '2024-01-20',
        checkOut: '2024-01-25',
        nights: 5,
        amount: 750.0,
        status: 'processing',
      ),
    ];
  }

  List<ReviewSummary> _getDummyReviewSummaries() {
    return [
      const ReviewSummary(
        id: 1,
        guestName: 'فاطمة الزهراء',
        propertyName: 'شقة فاخرة في الرياض',
        rating: 5.0,
        comment: 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي',
        date: '2024-01-10',
      ),
      const ReviewSummary(
        id: 2,
        guestName: 'عبدالله السعد',
        propertyName: 'فيلا مع مسبح',
        rating: 4.5,
        comment: 'إقامة جميلة، المسبح رائع والفيلا واسعة',
        date: '2024-01-08',
      ),
    ];
  }

  List<EarningsData> _getDummyEarningsChart() {
    return [
      const EarningsData(month: 'Jan', amount: 1200, monthNumber: 1),
      const EarningsData(month: 'Feb', amount: 1800, monthNumber: 2),
      const EarningsData(month: 'Mar', amount: 1500, monthNumber: 3),
      const EarningsData(month: 'Apr', amount: 2200, monthNumber: 4),
      const EarningsData(month: 'May', amount: 1900, monthNumber: 5),
      const EarningsData(month: 'Jun', amount: 2450, monthNumber: 6),
    ];
  }

  List<BookingsData> _getDummyBookingsChart() {
    return [
      const BookingsData(month: 'Jan', count: 8, monthNumber: 1),
      const BookingsData(month: 'Feb', count: 12, monthNumber: 2),
      const BookingsData(month: 'Mar', count: 10, monthNumber: 3),
      const BookingsData(month: 'Apr', count: 15, monthNumber: 4),
      const BookingsData(month: 'May', count: 13, monthNumber: 5),
      const BookingsData(month: 'Jun', count: 18, monthNumber: 6),
    ];
  }
}
