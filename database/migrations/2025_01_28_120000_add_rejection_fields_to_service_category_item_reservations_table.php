<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('service_category_item_reservations', function (Blueprint $table) {
            $table->string('status')->default('pending')->after('confirmed')
                ->comment('pending, confirmed, rejected, cancelled');
            $table->text('rejection_reason')->nullable()->after('status');
            $table->timestamp('rejected_at')->nullable()->after('rejection_reason');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('service_category_item_reservations', function (Blueprint $table) {
            $table->dropColumn(['status', 'rejection_reason', 'rejected_at']);
        });
    }
};
