<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ReservationResource extends JsonResource
{
    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection)
    {
        parent::__construct($collection);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $resource = [
            'id' => (int) $this->id,
            'reservation_date' => $this->reservation_date,
            'reservation_from' => $this->reservation_from,
            'reservation_to' => $this->reservation_to,
            'confirmed' => (bool) $this->confirmed,
            'status' => $this->status ?? 'pending',
            'rejection_reason' => $this->rejection_reason,
            'rejected_at' => $this->rejected_at,
            'service_category_item_id' => (int) $this->service_category_item_id,
            'user_id' => (int) $this->user_id,
            'item' => new ServiceCategoryItemResource($this->serviceCategoryItem),
            'user' => $this->when($this->user, [
                'id' => $this->user?->id,
                'full_name' => $this->user?->full_name,
                'phone' => $this->user?->phone,
                'email' => $this->user?->email,
            ]),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
        return $resource;
    }

}
