import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gather_point/feature/host/widgets/edit_property_wizard.dart';
import 'package:gather_point/feature/host/presentation/cubit/property_creation_cubit.dart';
import 'package:gather_point/feature/host/data/models/my_listing_model.dart';
import 'package:gather_point/feature/dashboard/data/services/properties_api_service.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';

// Mock classes
class MockPropertiesApiService extends PropertiesApiService {
  MockPropertiesApiService() : super(MockDioConsumer());
}

class MockDioConsumer extends DioConsumer {
  MockDioConsumer() : super(dio: Dio(), profileBox: MockBox<UserEntity>());
}

class MockBox<T> implements Box<T> {
  @override
  dynamic noSuchMethod(Invocation invocation) => null;
}

void main() {
  group('EditPropertyWizard Tests', () {
    late PropertyCreationCubit cubit;
    late MyListingModel mockProperty;

    setUp(() {
      // Setup GetIt for dependency injection
      if (!GetIt.instance.isRegistered<PropertiesApiService>()) {
        GetIt.instance.registerLazySingleton<PropertiesApiService>(
          () => MockPropertiesApiService(),
        );
      }

      cubit = PropertyCreationCubit(MockPropertiesApiService());

      // Create mock property data
      mockProperty = MyListingModel(
        id: 1,
        title: 'Test Property',
        content: 'Test Description',
        price: 100.0,
        weekendPrice: 150.0,
        weeklyPrice: 600.0,
        monthlyPrice: 2000.0,
        status: 'active',
        isAvailable: true,
        views: 10,
        bookings: 2,
        reviewCount: 5,
        galleryImages: ['image1.jpg', 'image2.jpg'],
        category: ServiceCategory(id: 1, title: 'Accommodation', icon: 'home', image: '', order: 1),
        noGuests: 4,
        beds: 2,
        baths: 1,
        lat: 24.7136,
        lon: 46.6753,
        address: 'Test Address',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        facilities: [],
        hasActiveReservations: false,
        pendingReservations: 0,
      );
    });

    tearDown(() {
      cubit.close();
    });

    testWidgets('EditPropertyWizard renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<PropertyCreationCubit>(
            create: (context) => cubit,
            child: EditPropertyWizard(propertyData: mockProperty),
          ),
        ),
      );

      // Verify the wizard renders
      expect(find.byType(EditPropertyWizard), findsOneWidget);
      
      // Verify step indicator is present
      expect(find.byType(Container), findsWidgets);
      
      // Verify basic info step is shown first
      expect(find.text('المعلومات الأساسية'), findsOneWidget);
    });

    testWidgets('Form fields are pre-populated with existing data', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<PropertyCreationCubit>(
            create: (context) => cubit,
            child: EditPropertyWizard(propertyData: mockProperty),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check if title field is pre-populated
      expect(find.text('Test Property'), findsOneWidget);
      
      // Check if description field is pre-populated
      expect(find.text('Test Description'), findsOneWidget);
    });

    testWidgets('Navigation between steps works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<PropertyCreationCubit>(
            create: (context) => cubit,
            child: EditPropertyWizard(propertyData: mockProperty),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the next button
      final nextButton = find.text('التالي');
      if (nextButton.evaluate().isNotEmpty) {
        await tester.tap(nextButton);
        await tester.pumpAndSettle();
        
        // Verify we moved to the next step
        expect(find.text('الفئة'), findsOneWidget);
      }
    });

    testWidgets('Step validation prevents navigation with invalid data', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<PropertyCreationCubit>(
            create: (context) => cubit,
            child: EditPropertyWizard(propertyData: mockProperty),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Clear the title field to make it invalid
      final titleField = find.byType(TextFormField).first;
      await tester.enterText(titleField, '');
      await tester.pumpAndSettle();

      // Try to navigate to next step
      final nextButton = find.text('التالي');
      if (nextButton.evaluate().isNotEmpty) {
        await tester.tap(nextButton);
        await tester.pumpAndSettle();
        
        // Should still be on the first step due to validation
        expect(find.text('المعلومات الأساسية'), findsOneWidget);
      }
    });

    test('Form data extraction works correctly', () {
      // This would test the _getFormData method
      // Since it's a private method, we'd need to test it indirectly
      // through the submit functionality
      expect(mockProperty.title, equals('Test Property'));
      expect(mockProperty.price, equals(100.0));
      expect(mockProperty.noGuests, equals(4));
    });
  });
}
